#!/usr/bin/env python3
"""
文档分类测试脚本
测试新的强制映射 + 智能分类机制
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.enhanced_document_manager import EnhancedDocumentManager
from src.utils.supplementary_classifier import SupplementaryMaterialClassifier

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_document_classification():
    """测试文档分类功能"""
    logger.info("=== 文档分类测试 ===")
    
    # 查找测试数据
    test_dirs = ["data0724", "data", "test_data"]
    test_dir = None
    
    for dir_name in test_dirs:
        if os.path.exists(dir_name):
            # 查找包含PDF文件的子目录
            for item in os.listdir(dir_name):
                item_path = os.path.join(dir_name, item)
                if os.path.isdir(item_path):
                    pdf_files = [f for f in os.listdir(item_path) if f.endswith('.pdf')]
                    if pdf_files:
                        test_dir = item_path
                        break
            if test_dir:
                break
    
    if not test_dir:
        logger.error("未找到包含PDF文件的测试目录")
        return False
    
    logger.info(f"使用测试目录: {test_dir}")
    
    # 创建文档管理器
    doc_manager = EnhancedDocumentManager(test_dir)
    
    # 显示分类结果
    logger.info("\n=== 分类结果概要 ===")
    summary = doc_manager.get_document_summary()
    
    logger.info(f"总文档数: {summary['total_documents']}")
    logger.info(f"标准文档数: {summary['standard_documents']['count']}")
    logger.info(f"补充材料数: {summary['supplementary_materials']['count']}")
    logger.info(f"分类策略: {summary['classification_strategy']}")
    
    # 显示详细分类信息
    logger.info("\n=== 详细分类信息 ===")
    details = doc_manager.get_classification_details()
    
    logger.info("标准文档 (强制映射):")
    for doc_type, doc_info in details["standard_documents"].items():
        logger.info(f"  {doc_type}: {doc_info['filename']} -> {doc_info['type_name']}")
    
    if details["supplementary_materials"]:
        logger.info("\n补充材料 (智能分类):")
        for doc_type, doc_info in details["supplementary_materials"].items():
            original_type = doc_info.get('original_type', doc_type.split('_')[0])
            logger.info(f"  {doc_type}: {doc_info['filename']} -> {doc_info['type_name']} (原始类型: {original_type})")
    
    # 检查文档完整性
    logger.info("\n=== 文档完整性检查 ===")
    is_complete, missing = doc_manager.validate_document_completeness()
    
    if is_complete:
        logger.info("✅ 所有必需文档齐全")
    else:
        logger.warning(f"⚠️  缺少必需文档: {missing}")
    
    return True


def test_supplementary_classifier():
    """测试补充材料分类器"""
    logger.info("\n=== 补充材料分类器测试 ===")
    
    classifier = SupplementaryMaterialClassifier()
    
    # 显示支持的补充材料类型
    logger.info("支持的补充材料类型:")
    for doc_type, type_name in classifier.get_all_supplementary_types().items():
        logger.info(f"  {doc_type}: {type_name}")
    
    # 测试文件名分类
    test_filenames = [
        "谈话记录.pdf",
        "面谈记录表.pdf", 
        "岗位资格证书.pdf",
        "专业技术证书.pdf",
        "工作证明.pdf",
        "在职证明.pdf",
        "其他材料.pdf",
        "补充说明.pdf",
        "人力资源系统履历截图.pdf"  # 这个应该被归类为其他补充材料
    ]
    
    logger.info("\n文件名分类测试:")
    for filename in test_filenames:
        # 创建临时文件路径进行测试
        temp_path = f"/tmp/{filename}"
        
        # 只进行文件名分类，不进行内容分析
        doc_type = classifier.classify_supplementary_material(temp_path, use_content_analysis=False)
        type_name = classifier.get_type_name(doc_type)
        
        logger.info(f"  {filename} -> {doc_type}-{type_name}")
    
    return True


def test_edge_cases():
    """测试边界情况"""
    logger.info("\n=== 边界情况测试 ===")
    
    # 测试空目录
    logger.info("测试空目录...")
    empty_dir = "/tmp/empty_test_dir"
    os.makedirs(empty_dir, exist_ok=True)
    
    try:
        doc_manager = EnhancedDocumentManager(empty_dir)
        summary = doc_manager.get_document_summary()
        logger.info(f"空目录结果: 总文档数 {summary['total_documents']}")
    except Exception as e:
        logger.error(f"空目录测试失败: {e}")
    finally:
        if os.path.exists(empty_dir):
            os.rmdir(empty_dir)
    
    # 测试不存在的目录
    logger.info("测试不存在的目录...")
    try:
        doc_manager = EnhancedDocumentManager("/nonexistent/directory")
        summary = doc_manager.get_document_summary()
        logger.info(f"不存在目录结果: 总文档数 {summary['total_documents']}")
    except Exception as e:
        logger.error(f"不存在目录测试失败: {e}")
    
    return True


def main():
    """主测试函数"""
    logger.info("开始文档分类系统测试")
    
    success = True
    
    # 测试1: 文档分类功能
    try:
        if not test_document_classification():
            success = False
    except Exception as e:
        logger.error(f"文档分类测试失败: {e}")
        success = False
    
    # 测试2: 补充材料分类器
    try:
        if not test_supplementary_classifier():
            success = False
    except Exception as e:
        logger.error(f"补充材料分类器测试失败: {e}")
        success = False
    
    # 测试3: 边界情况
    try:
        if not test_edge_cases():
            success = False
    except Exception as e:
        logger.error(f"边界情况测试失败: {e}")
        success = False
    
    # 总结
    logger.info("\n=== 测试总结 ===")
    if success:
        logger.info("🎉 所有测试通过！")
        logger.info("\n新的分类机制特点:")
        logger.info("✅ 前6个文件强制映射到标准文档类型")
        logger.info("✅ 第7个及以后文件使用智能分类")
        logger.info("✅ 支持文件名 + 内容双重分析")
        logger.info("✅ 处理重复类型的情况")
        logger.info("✅ 提供详细的分类信息")
    else:
        logger.error("❌ 部分测试失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
