#!/usr/bin/env python3
"""
测试LLM Token使用统计功能
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.intelligent_audit_service import IntelligentAuditService
from src.tools.audit_report_generator import AuditReportGenerator


def test_token_statistics():
    """测试Token使用统计功能"""
    print("🧪 测试LLM Token使用统计功能")
    print("=" * 60)
    
    try:
        # 1. 初始化审核服务
        print("1. 初始化审核服务...")
        audit_service = IntelligentAuditService()
        
        # 2. 执行审核
        applicant_dir = "data0724/20160212111(河北石家庄）"
        print(f"2. 执行审核: {applicant_dir}")
        
        audit_result = audit_service.audit_applicant(applicant_dir)
        print("✅ 审核完成")
        
        # 3. 检查Token使用统计
        print("3. 分析Token使用统计...")
        token_analysis = audit_result.get("token_usage_analysis", {})
        
        if "error" in token_analysis:
            print(f"❌ Token分析失败: {token_analysis.get('error')}")
            return False
        
        # 显示输入Token分解
        input_breakdown = token_analysis.get("input_breakdown", {})
        print("   📊 输入Token分解:")
        print(f"     系统提示词: {input_breakdown.get('system_message', {}).get('estimated_tokens', 0)} tokens")
        print(f"     审核规则文档: {input_breakdown.get('rules_content', {}).get('estimated_tokens', 0)} tokens")
        print(f"     提示词模板: {input_breakdown.get('prompt_template', {}).get('estimated_tokens', 0)} tokens")
        print(f"     文档内容总计: {input_breakdown.get('total_document_tokens', 0)} tokens")
        print(f"     固定部分总计: {input_breakdown.get('total_fixed_tokens', 0)} tokens")
        print(f"     输入总计: {input_breakdown.get('total_input_tokens', 0)} tokens")
        
        # 显示各文档Token使用
        documents = input_breakdown.get("documents", {})
        print("\n   📄 各文档Token使用:")
        for doc_type, doc_info in documents.items():
            tokens = doc_info.get("estimated_tokens", 0)
            filename = doc_info.get("filename", "未知文件")
            content_length = doc_info.get("content_length", 0)
            print(f"     - {doc_type}: {tokens} tokens ({content_length} 字符) - {filename}")
        
        # 显示文档统计
        doc_stats = token_analysis.get("document_statistics", {})
        print("\n   📈 文档统计:")
        print(f"     文档数量: {doc_stats.get('document_count', 0)}")
        print(f"     平均每文档Token: {doc_stats.get('average_tokens_per_document', 0)}")
        
        largest_doc = doc_stats.get("largest_document", ("无", {"estimated_tokens": 0}))
        smallest_doc = doc_stats.get("smallest_document", ("无", {"estimated_tokens": 0}))
        print(f"     最大文档: {largest_doc[0]} ({largest_doc[1]['estimated_tokens']} tokens)")
        print(f"     最小文档: {smallest_doc[0]} ({smallest_doc[1]['estimated_tokens']} tokens)")
        
        # 显示LLM响应统计
        llm_stats = token_analysis.get("llm_response_stats", {})
        print("\n   🤖 LLM响应统计:")
        if llm_stats:
            input_tokens = llm_stats.get("input_tokens", {})
            print(f"     输入Token (估算): {input_tokens.get('total', 0)}")
            print(f"       - 系统消息: {input_tokens.get('system_message', 0)}")
            print(f"       - 用户消息: {input_tokens.get('user_message', 0)}")
            print(f"     输出Token (估算): {llm_stats.get('output_tokens', 0)}")
            print(f"     总Token (估算): {llm_stats.get('total_tokens', 0)}")
            print(f"     使用模型: {llm_stats.get('model', '未知')}")
            
            # 如果有实际API使用统计
            actual_usage = llm_stats.get("actual_usage", {})
            if actual_usage:
                print(f"     实际API使用:")
                print(f"       - 输入Token: {actual_usage.get('prompt_tokens', 0)}")
                print(f"       - 输出Token: {actual_usage.get('completion_tokens', 0)}")
                print(f"       - 总Token: {actual_usage.get('total_tokens', 0)}")
        else:
            print("     无LLM响应统计信息")
        
        # 显示成本估算
        cost_estimation = token_analysis.get("cost_estimation", {})
        print("\n   💰 成本估算:")
        print(f"     模型: {cost_estimation.get('model', '未知')}")
        print(f"     估算输入Token: {cost_estimation.get('estimated_input_tokens', 0)}")
        print(f"     估算输出Token: {cost_estimation.get('estimated_output_tokens', 0)}")
        print(f"     说明: {cost_estimation.get('note', '无')}")
        
        # 4. 生成包含Token统计的报告
        print("\n4. 生成包含Token统计的报告...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f"results/token_stats_audit_result_{timestamp}.json"
        txt_filename = f"results/token_stats_audit_report_{timestamp}.txt"
        
        # 保存JSON结果
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(audit_result, f, ensure_ascii=False, indent=2)
        
        # 生成TXT报告
        report_generator = AuditReportGenerator()
        txt_report = report_generator.generate_audit_report(audit_result)
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write(txt_report)
        
        print(f"✅ 报告生成成功:")
        print(f"   JSON: {json_filename}")
        print(f"   TXT:  {txt_filename}")
        
        # 5. 分析Token使用效率
        print("\n5. Token使用效率分析:")
        total_input = input_breakdown.get("total_input_tokens", 0)
        document_tokens = input_breakdown.get("total_document_tokens", 0)
        fixed_tokens = input_breakdown.get("total_fixed_tokens", 0)
        
        if total_input > 0:
            doc_percentage = round((document_tokens / total_input) * 100, 2)
            fixed_percentage = round((fixed_tokens / total_input) * 100, 2)
            
            print(f"   文档内容占比: {doc_percentage}% ({document_tokens}/{total_input})")
            print(f"   固定部分占比: {fixed_percentage}% ({fixed_tokens}/{total_input})")
            
            if doc_percentage < 50:
                print("   ⚠️ 文档内容占比较低，固定提示词可能过长")
            elif doc_percentage > 80:
                print("   ✅ 文档内容占比合理，Token使用效率良好")
            else:
                print("   ✅ Token使用分布合理")
        
        # 6. 总结
        print("\n📊 Token使用统计总结:")
        print(f"   总输入Token: {total_input}")
        print(f"   文档处理数量: {doc_stats.get('document_count', 0)}")
        print(f"   平均每文档Token: {doc_stats.get('average_tokens_per_document', 0)}")
        print(f"   使用模型: {cost_estimation.get('model', '未知')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_token_statistics()
    if success:
        print("\n🎉 Token统计功能测试完成！")
    else:
        print("\n❌ 测试失败")
        sys.exit(1)
