#!/usr/bin/env python3
"""
智能审核系统命令行工具
统一的命令行接口，支持审核、测试、配置管理等功能
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import override_config_from_args, validate_config
from src.core.intelligent_audit_service import IntelligentAuditService
from src.utils.cache_manager import CacheManager
from src.utils.config_manager import ConfigManager
from src.utils.timestamp_utils import extract_timestamp_from_audit_id, generate_audit_result_filename, generate_audit_report_filename
from src.tools.audit_report_generator import AuditReportGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_base_parser():
    """创建基础解析器"""
    parser = argparse.ArgumentParser(
        description="智能审核系统命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 审核单个申请人
  python audit_cli.py audit data0724/20080766971（山西太原）
  
  # 批量审核多个申请人
  python audit_cli.py batch-audit data0724/ --output results/
  
  # 运行系统测试
  python audit_cli.py test --data-dir data0724 --with-llm
  
  # 管理缓存
  python audit_cli.py cache --stats
  python audit_cli.py cache --clean 7
  
  # 配置管理
  python audit_cli.py config generate --template basic
  python audit_cli.py config validate
        """
    )
    
    # 全局选项
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--quiet", "-q", action="store_true", help="静默模式")
    
    # 通用配置覆盖选项
    config_group = parser.add_argument_group("配置覆盖选项")
    config_group.add_argument("--data-dir", help="数据目录路径")
    config_group.add_argument("--cache-dir", help="缓存目录路径")
    config_group.add_argument("--output-dir", help="输出目录路径")
    config_group.add_argument("--page-workers", type=int, help="页面级并发线程数")
    config_group.add_argument("--doc-workers", type=int, help="文档级并发线程数")
    config_group.add_argument("--user-workers", type=int, help="用户级并发线程数")
    config_group.add_argument("--disable-cache", action="store_true", help="禁用缓存")
    config_group.add_argument("--api-rate-limit", type=int, help="API调用频率限制")
    config_group.add_argument("--max-retries", type=int, help="最大重试次数")

    # 缓存控制参数
    cache_group = parser.add_argument_group("缓存控制选项")
    cache_group.add_argument("--clear-cache-before-run", action="store_true", help="运行前清空所有缓存")
    cache_group.add_argument("--clear-images-before-run", action="store_true", help="运行前清空图片缓存")
    cache_group.add_argument("--clear-extractions-before-run", action="store_true", help="运行前清空提取结果缓存")
    
    return parser


def setup_logging(args):
    """设置日志"""
    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    elif args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        logging.getLogger().setLevel(logging.INFO)


def apply_config(args):
    """应用配置"""
    # 应用配置文件
    if args.config:
        config_manager = ConfigManager()
        if not config_manager.apply_config_file(args.config):
            logger.error("配置文件应用失败")
            return False
    
    # 应用命令行参数覆盖
    args_dict = {k: v for k, v in vars(args).items() if v is not None}
    override_config_from_args(args_dict)
    
    return True


def handle_cache_control_before_run(args):
    """处理运行前的缓存控制"""
    from src.utils.cache_manager import CacheManager

    # 检查是否需要清理缓存
    clear_all = getattr(args, 'clear_cache_before_run', False)
    clear_images = getattr(args, 'clear_images_before_run', False)
    clear_extractions = getattr(args, 'clear_extractions_before_run', False)

    if clear_all or clear_images or clear_extractions:
        cache_manager = CacheManager()
        cache_manager.auto_clear_cache_if_needed(
            clear_before_run=clear_all,
            clear_images=clear_images,
            clear_extractions=clear_extractions
        )


def cmd_audit(args):
    """审核单个申请人"""
    logger.info(f"开始审核申请人: {args.applicant_dir}")

    # 检查缓存控制参数
    handle_cache_control_before_run(args)

    if not os.path.exists(args.applicant_dir):
        logger.error(f"申请人目录不存在: {args.applicant_dir}")
        return False
    
    try:
        service = IntelligentAuditService()
        result = service.audit_applicant(args.applicant_dir)
        
        if not result:
            logger.error("审核失败")
            return False
        
        # 保存结果
        output_dir = args.output_dir or "results"
        os.makedirs(output_dir, exist_ok=True)

        applicant_name = os.path.basename(args.applicant_dir.rstrip('/'))

        # 从audit_id中提取时间戳
        audit_id = result.get("audit_id", "")
        timestamp = extract_timestamp_from_audit_id(audit_id)

        output_filename = generate_audit_result_filename(applicant_name, timestamp)
        output_file = os.path.join(output_dir, output_filename)
        
        import json
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        logger.info(f"审核完成，JSON结果已保存到: {output_file}")

        # 自动生成TXT报告
        try:
            report_generator = AuditReportGenerator()
            report_content = report_generator.generate_audit_report(result)

            # 生成TXT报告文件名（使用相同时间戳）
            report_filename = generate_audit_report_filename(applicant_name, timestamp)
            report_file = os.path.join(output_dir, report_filename)

            # 保存TXT报告
            report_generator.save_report_to_file(report_content, report_file)
            logger.info(f"TXT报告已保存到: {report_file}")

        except Exception as e:
            logger.warning(f"生成TXT报告失败: {e}")

        # 显示关键信息
        if result.get("basic_verification", {}).get("overall_result", {}).get("result"):
            verification_result = result["basic_verification"]["overall_result"]["result"]
            logger.info(f"基本验证结果: {verification_result}")

        if result.get("risk_assessment", {}).get("overall_risk", {}).get("result"):
            risk_result = result["risk_assessment"]["overall_risk"]["result"]
            logger.info(f"风险评估结果: {risk_result}")

        return True
        
    except Exception as e:
        logger.error(f"审核过程中发生错误: {e}")
        return False


def cmd_batch_audit(args):
    """批量审核"""
    logger.info(f"开始批量审核: {args.data_dir}")

    # 检查缓存控制参数
    handle_cache_control_before_run(args)

    if not os.path.exists(args.data_dir):
        logger.error(f"数据目录不存在: {args.data_dir}")
        return False
    
    # 查找所有申请人目录
    applicant_dirs = []
    for item in os.listdir(args.data_dir):
        item_path = os.path.join(args.data_dir, item)
        if os.path.isdir(item_path):
            # 检查是否包含PDF文件
            pdf_files = [f for f in os.listdir(item_path) if f.endswith('.pdf')]
            if pdf_files:
                applicant_dirs.append(item_path)
    
    if not applicant_dirs:
        logger.error(f"在 {args.data_dir} 中未找到包含PDF文件的申请人目录")
        return False
    
    logger.info(f"找到 {len(applicant_dirs)} 个申请人目录")
    
    # 限制处理数量
    if args.max_applicants and len(applicant_dirs) > args.max_applicants:
        applicant_dirs = applicant_dirs[:args.max_applicants]
        logger.info(f"限制处理数量为 {args.max_applicants} 个")
    
    # 批量处理
    try:
        from src.core.batch_audit_processor import BatchAuditProcessor
        
        processor = BatchAuditProcessor(use_concurrent_extraction=not args.disable_concurrent)
        results = processor.process_batch(applicant_dirs)
        
        # 保存批量结果
        output_dir = args.output_dir or "results"
        os.makedirs(output_dir, exist_ok=True)
        
        batch_result_file = os.path.join(output_dir, "batch_audit_results.json")
        
        import json
        with open(batch_result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"批量审核完成，结果已保存到: {batch_result_file}")
        
        # 显示统计信息
        stats = processor.get_processing_stats()
        logger.info(f"处理统计: 总计 {stats['total_tasks']} 个，成功 {stats['completed_tasks']} 个，失败 {stats['failed_tasks']} 个")
        
        return True
        
    except Exception as e:
        logger.error(f"批量审核过程中发生错误: {e}")
        return False


def cmd_test(args):
    """运行系统测试"""
    logger.info("运行系统测试")

    # 检查缓存控制参数
    handle_cache_control_before_run(args)

    # 导入测试模块
    import test_complete_system
    
    # 构建测试参数
    test_args = argparse.Namespace()
    for key, value in vars(args).items():
        setattr(test_args, key, value)
    
    # 运行测试
    try:
        # 临时替换sys.argv以传递参数给测试脚本
        original_argv = sys.argv
        sys.argv = ['test_complete_system.py']
        
        if args.with_llm:
            sys.argv.append('--with-llm')
        if args.with_vlm:
            sys.argv.append('--with-vlm')
        
        # 调用测试主函数
        success = test_complete_system.main()
        
        # 恢复原始argv
        sys.argv = original_argv
        
        return success
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        return False


def cmd_cache(args):
    """缓存管理"""
    cache_manager = CacheManager(args.cache_dir or "cache")
    
    if args.stats:
        cache_manager.print_cache_report()
        return True
    
    elif args.clean is not None:
        logger.info(f"清理 {args.clean} 天前的缓存...")
        results = cache_manager.clean_old_cache(args.clean)
        logger.info(f"清理完成: 图片缓存 {results['images_removed']} 个，提取缓存 {results['extractions_removed']} 个")
        logger.info(f"释放空间: {results['total_size_freed_mb']:.2f} MB")
        return True
    
    elif args.validate:
        logger.info("验证缓存完整性...")
        issues = cache_manager.validate_cache_integrity()
        total_issues = sum(len(issue_list) for issue_list in issues.values())
        
        if total_issues == 0:
            logger.info("✅ 缓存完整性检查通过")
        else:
            logger.warning(f"⚠️  发现 {total_issues} 个缓存问题")
            for issue_type, issue_list in issues.items():
                if issue_list:
                    logger.warning(f"  {issue_type}: {len(issue_list)} 个问题")
        
        return total_issues == 0
    
    elif args.clear_all:
        logger.info("清空所有缓存...")
        cache_manager.clear_all_cache()
        logger.info("✅ 所有缓存已清空")
        return True

    elif args.clear_images:
        logger.info("清空图片缓存...")
        cache_manager.clear_image_cache()
        logger.info("✅ 图片缓存已清空")
        return True

    elif args.clear_extractions:
        logger.info("清空提取结果缓存...")
        cache_manager.clear_extraction_cache()
        logger.info("✅ 提取结果缓存已清空")
        return True
    
    else:
        logger.error("请指定缓存操作: --stats, --clean, --validate, 或 --clear-all")
        return False


def cmd_config(args):
    """配置管理"""
    config_manager = ConfigManager()
    
    if args.config_command == "generate":
        success = config_manager.generate_config_file(args.template, args.output)
        if success:
            logger.info(f"配置文件已生成: {args.output}")
        return success
    
    elif args.config_command == "validate":
        return config_manager.validate_current_config()
    
    elif args.config_command == "show":
        config_manager.show_current_config()
        return True
    
    else:
        logger.error("未知的配置命令")
        return False


def main():
    """主函数"""
    parser = create_base_parser()
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 审核命令
    audit_parser = subparsers.add_parser("audit", help="审核单个申请人", parents=[create_base_parser()], add_help=False)
    audit_parser.add_argument("applicant_dir", help="申请人目录路径")

    # 批量审核命令
    batch_parser = subparsers.add_parser("batch-audit", help="批量审核", parents=[create_base_parser()], add_help=False)
    batch_parser.add_argument("data_dir", help="数据目录路径")
    batch_parser.add_argument("--max-applicants", type=int, help="最大处理申请人数量")
    batch_parser.add_argument("--disable-concurrent", action="store_true", help="禁用并发处理")

    # 测试命令
    test_parser = subparsers.add_parser("test", help="运行系统测试", parents=[create_base_parser()], add_help=False)
    test_parser.add_argument("--with-llm", action="store_true", help="启用LLM测试")
    test_parser.add_argument("--with-vlm", action="store_true", help="启用VLM测试")
    test_parser.add_argument("--max-test-files", type=int, default=3, help="最大测试文件数")
    
    # 缓存管理命令
    cache_parser = subparsers.add_parser("cache", help="缓存管理", parents=[create_base_parser()], add_help=False)
    cache_group = cache_parser.add_mutually_exclusive_group(required=True)
    cache_group.add_argument("--stats", action="store_true", help="显示缓存统计")
    cache_group.add_argument("--clean", type=int, metavar="DAYS", help="清理N天前的缓存")
    cache_group.add_argument("--validate", action="store_true", help="验证缓存完整性")
    cache_group.add_argument("--clear-all", action="store_true", help="清空所有缓存")
    cache_group.add_argument("--clear-images", action="store_true", help="只清空图片缓存")
    cache_group.add_argument("--clear-extractions", action="store_true", help="只清空提取结果缓存")

    # 配置管理命令
    config_parser = subparsers.add_parser("config", help="配置管理", parents=[create_base_parser()], add_help=False)
    config_subparsers = config_parser.add_subparsers(dest="config_command", help="配置命令")
    
    # 生成配置文件
    gen_parser = config_subparsers.add_parser("generate", help="生成配置文件")
    gen_parser.add_argument("--template", choices=["basic", "advanced", "development", "production"], 
                           default="basic", help="配置模板")
    gen_parser.add_argument("--output", default="user_config.yaml", help="输出文件名")
    
    # 验证配置
    config_subparsers.add_parser("validate", help="验证当前配置")
    
    # 显示配置
    config_subparsers.add_parser("show", help="显示当前配置")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args)
    
    # 应用配置
    if not apply_config(args):
        return False
    
    # 执行命令
    if args.command == "audit":
        return cmd_audit(args)
    elif args.command == "batch-audit":
        return cmd_batch_audit(args)
    elif args.command == "test":
        return cmd_test(args)
    elif args.command == "cache":
        return cmd_cache(args)
    elif args.command == "config":
        return cmd_config(args)
    else:
        parser.print_help()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
