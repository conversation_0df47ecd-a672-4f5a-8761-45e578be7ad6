#!/usr/bin/env python3
"""
测试JSON修复功能
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.llm_client import LLMClient

def test_json_fix():
    """测试JSON修复功能"""
    
    print("🔧 测试JSON修复功能")
    print("=" * 60)
    
    # 创建LLM客户端实例
    llm_client = LLMClient()
    
    # 测试有问题的JSON字符串（从实际错误中提取）
    problematic_json = '''```json
{
    "basic_info_verification": {
        "material_validity": {
            "completeness": {
                "identity_card": {"status": "符合", "details": "身份证复印件完整"},
                "education_materials": {"status": "符合", "details": "学历材料完整"}
            },
            "clarity": {"status": "不符合", "details": "材料存在问题"},
            "application_form": {
                "format_integrity": {
                    "status": "不符合",
                    "details": "申请表包含多余字段"
                }
            }
        },
        "information_consistency": {
            "identity_verification": {
                "status": "不符合",
                "details": "身份信息在不同文件中不一致",
                "document_comparison": {
                    "application_form_vs_id_card": {"status": "符合", "details": "申请表与身份证复印件一致"},
                    "application_form_vs_credit_report": {"status": "不符合", "details": "申请表与征信报告不一致"},
                    "application_form_vs_hr_screenshot":>{"status": "                                                                           }},
                    "cross_document_consistency": {"status": "不符合", "details": "所有文件间身份信息不一致"}
                }
            }
        }
    }
}
```'''
    
    print("1. 原始有问题的JSON:")
    print("   包含语法错误: application_form_vs_hr_screenshot\":>{\"status\": \"...")
    
    # 测试清理功能
    print("\n2. 执行JSON清理...")
    try:
        cleaned_json = llm_client._clean_json_response(problematic_json)
        print("✅ JSON清理完成")
        
        # 尝试解析清理后的JSON
        print("\n3. 尝试解析清理后的JSON...")
        parsed_json = json.loads(cleaned_json)
        print("✅ JSON解析成功！")
        
        # 检查修复后的结构
        print("\n4. 检查修复后的结构...")
        document_comparison = parsed_json.get("basic_info_verification", {}).get("information_consistency", {}).get("identity_verification", {}).get("document_comparison", {})
        
        hr_screenshot_result = document_comparison.get("application_form_vs_hr_screenshot", {})
        print(f"   修复后的hr_screenshot字段: {hr_screenshot_result}")
        
        if hr_screenshot_result.get("status") and hr_screenshot_result.get("details"):
            print("✅ 不完整字段已成功修复")
        else:
            print("⚠️  字段修复可能不完整")
        
        # 保存修复后的JSON
        os.makedirs("results", exist_ok=True)
        with open("results/fixed_json_test.json", 'w', encoding='utf-8') as f:
            json.dump(parsed_json, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 修复后的JSON已保存到: results/fixed_json_test.json")
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析仍然失败: {e}")
        print(f"   错误位置: 第{e.lineno}行，第{e.colno}列")
        
        # 保存清理后但仍有问题的JSON用于调试
        with open("results/debug_cleaned_json.txt", 'w', encoding='utf-8') as f:
            f.write(cleaned_json)
        print(f"   清理后的JSON已保存到: results/debug_cleaned_json.txt")
        
        return False
    
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False
    
    print("\n🎉 JSON修复功能测试完成！")
    return True

def test_real_audit_with_fix():
    """测试实际审核中的JSON修复"""
    
    print("\n" + "=" * 60)
    print("🧪 测试实际审核中的JSON修复")
    print("=" * 60)
    
    from src.core.intelligent_audit_service import IntelligentAuditService
    
    # 初始化审核服务
    audit_service = IntelligentAuditService()
    
    # 执行审核
    applicant_dir = "data0724/20160212111(河北石家庄）"
    if not os.path.exists(applicant_dir):
        print(f"❌ 申请人目录不存在: {applicant_dir}")
        return False
    
    print(f"执行审核: {applicant_dir}")
    try:
        audit_result = audit_service.audit_applicant(applicant_dir)
        
        # 检查是否还有JSON解析错误
        rule_based_analysis = audit_result.get("rule_based_analysis", {})
        
        if "error" in rule_based_analysis:
            print(f"❌ 仍然存在JSON解析错误: {rule_based_analysis.get('error')}")
            return False
        else:
            print("✅ JSON解析成功，无错误！")
            
            # 检查材料清晰度评估结果
            clarity = rule_based_analysis.get("basic_info_verification", {}).get("material_validity", {}).get("clarity", {})
            print(f"   材料清晰度评估: {clarity.get('status', '未知')} - {clarity.get('details', '无详细信息')}")
            
            # 保存成功的结果
            os.makedirs("results", exist_ok=True)
            with open("results/successful_audit_result.json", 'w', encoding='utf-8') as f:
                json.dump(audit_result, f, ensure_ascii=False, indent=2)
            
            print("✅ 成功的审核结果已保存到: results/successful_audit_result.json")
            return True
        
    except Exception as e:
        print(f"❌ 审核失败: {e}")
        return False

if __name__ == "__main__":
    success1 = test_json_fix()
    success2 = test_real_audit_with_fix()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！JSON修复功能正常工作。")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")
        sys.exit(1)
