"""
配置文件
管理系统的各种配置参数
"""

import os
from typing import Dict, Any

# LLM配置
LLM_CONFIG = {
    # 支持的LLM提供商
    "provider": os.getenv("LLM_PROVIDER", "qwen"),  # deepseek, qwen
    
    # DeepSeek配置
    "deepseek": {
        "api_key": os.getenv("DEEPSEEK_API_KEY", "***********************************"),
        "base_url": os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com/v1"),
        "model": os.getenv("DEEPSEEK_MODEL", "deepseek-chat"),
        "temperature": 0.1,
        "max_tokens": 2000
    },
    
    # Qwen配置
    "qwen": {
        "api_key": os.getenv("QWEN_API_KEY", "sk-plpykhaoetmlvsvzoffnljxsgykhlpwwleewhugruiugglxc"),
        "base_url": os.getenv("QWEN_BASE_URL", "https://api.siliconflow.cn/v1"),
        "model": os.getenv("QWEN_MODEL", "Qwen/Qwen2.5-72B-Instruct-128K"),
        "temperature": 0.1,
        "max_tokens": 4000
    }
}

# VLM配置（只支持Qwen2.5-VL-72B通过硅基智能）
VLM_CONFIG = {
    "provider": "qwen",  # 固定使用qwen

    # Qwen VLM配置（通过硅基智能）
    "qwen": {
        "api_key": os.getenv("QWEN_VLM_API_KEY", "sk-plpykhaoetmlvsvzoffnljxsgykhlpwwleewhugruiugglxc"),
        "base_url": os.getenv("QWEN_VLM_BASE_URL", "https://api.siliconflow.cn/v1"),
        "model": os.getenv("QWEN_VLM_MODEL", "Qwen/Qwen2.5-VL-72B-Instruct"),
        "temperature": 0.1,
        "max_tokens": 2000
    }
}

# 文档处理配置
DOCUMENT_CONFIG = {
    # PDF转图片配置
    "pdf_to_image": {
        "dpi": 200,
        "format": "PNG",
        "thread_count": 4
    },
    
    # 缓存配置
    "cache": {
        "enabled": True,
        "cache_dir": "cache",
        "max_cache_size_mb": 1000,
        "cache_ttl_hours": 24
    },
    
    # 文档类型配置
    "supported_formats": [".pdf", ".docx", ".doc", ".png", ".jpg", ".jpeg"],
    
    # 提取配置
    "extraction": {
        "max_retries": 3,
        "timeout_seconds": 30,
        "concurrent_limit": 4
    },

    # VLM并发配置（保守设置，避免超过硅基流动API限制）
    "vlm_concurrency": {
        # 页面级并发（单文档内多页面）- 降低到2个线程
        "page_level_workers": 2,

        # 文档级并发（单用户内多文档）- 降低到3个线程
        "document_level_workers": 3,

        # 用户级并发（多用户）- 降低到2个线程
        "user_level_workers": 2,

        # API调用控制 - 更保守的限制
        "api_rate_limit": {
            "calls_per_minute": 30,  # 降低到30次/分钟
            "batch_delay_seconds": 2.0,  # 增加批次间延时到2秒
            "concurrent_limit": 6  # 最大同时并发数限制
        },

        # 错误处理 - 增强重试机制
        "retry_config": {
            "max_retries": 5,  # 增加重试次数
            "retry_delay_seconds": 3.0,  # 增加重试延时
            "exponential_backoff": True,
            "max_retry_delay": 30.0  # 最大重试延时30秒
        }
    }
}

# 审核配置
AUDIT_CONFIG = {
    # 规则文档目录
    "rules_dir": "规则文档",
    
    # 输出配置
    "output": {
        "default_dir": "results",
        "json_indent": 2,
        "generate_word": True,
        "generate_markdown": True
    },
    
    # 验证配置
    "verification": {
        "required_documents": [
            "申请表", "身份证复印件", "征信报告", 
            "教育背景", "邮政履历", "执行网公开信息"
        ],
        "identity_fields": [
            "姓名", "身份证号", "性别", "出生日期"
        ]
    },
    
    # 风险评估配置
    "risk_assessment": {
        "categories": [
            "信用卡情况", "负债情况", "非银机构贷款",
            "经营性贷款", "担保情况", "征信禁入/瑕疵", "异常信息"
        ],
        "criminal_record_rejection": True,
        "objective_recording": True
    }
}

# 日志配置
LOGGING_CONFIG = {
    "level": os.getenv("LOG_LEVEL", "INFO"),
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "audit.log",
    "max_file_size_mb": 10,
    "backup_count": 5
}

# 测试配置
TEST_CONFIG = {
    # 测试数据路径配置
    "data_paths": {
        "default_data_dir": os.getenv("TEST_DATA_DIR", "data0724"),
        "fallback_dirs": [
            "data0724",
            "data",
            "test_data",
            "samples"
        ],
        "supported_extensions": [".pdf", ".docx", ".doc", ".png", ".jpg", ".jpeg"]
    },

    # 测试执行配置
    "execution": {
        "enable_vlm_tests": os.getenv("ENABLE_VLM_TESTS", "false").lower() == "true",
        "enable_llm_tests": os.getenv("ENABLE_LLM_TESTS", "false").lower() == "true",
        "test_timeout_seconds": int(os.getenv("TEST_TIMEOUT", "300")),
        "max_test_files": int(os.getenv("MAX_TEST_FILES", "3"))
    },

    # 缓存控制配置
    "cache_control": {
        "clear_before_run": os.getenv("CLEAR_CACHE_BEFORE_RUN", "false").lower() == "true",
        "clear_images_before_run": os.getenv("CLEAR_IMAGES_BEFORE_RUN", "false").lower() == "true",
        "clear_extractions_before_run": os.getenv("CLEAR_EXTRACTIONS_BEFORE_RUN", "false").lower() == "true",
        "auto_clear_old_cache_days": int(os.getenv("AUTO_CLEAR_OLD_CACHE_DAYS", "7"))
    },

    # 性能测试配置
    "performance": {
        "cache_speedup_threshold": float(os.getenv("CACHE_SPEEDUP_THRESHOLD", "2.0")),
        "performance_test_iterations": int(os.getenv("PERF_TEST_ITERATIONS", "2")),
        "benchmark_single_file": True
    }
}

# 系统配置
SYSTEM_CONFIG = {
    "version": "2.0.0",
    "name": "智能审核系统",
    "description": "基于LLM和VLM的信贷资格审核系统",
    "author": "AI Assistant",
    "max_concurrent_audits": int(os.getenv("MAX_CONCURRENT_AUDITS", "3")),
    "temp_dir": os.getenv("TEMP_DIR", "temp"),
    "cleanup_temp_files": os.getenv("CLEANUP_TEMP_FILES", "true").lower() == "true",

    # 路径配置
    "paths": {
        "data_dir": os.getenv("DATA_DIR", "data0724"),
        "rules_dir": os.getenv("RULES_DIR", "规则文档"),
        "output_dir": os.getenv("OUTPUT_DIR", "results"),
        "cache_dir": os.getenv("CACHE_DIR", "cache"),
        "log_dir": os.getenv("LOG_DIR", "logs")
    }
}


def get_llm_config() -> Dict[str, Any]:
    """获取当前LLM配置"""
    provider = LLM_CONFIG["provider"]
    if provider not in LLM_CONFIG:
        raise ValueError(f"不支持的LLM提供商: {provider}")
    
    config = LLM_CONFIG[provider].copy()
    config["provider"] = provider
    return config


def get_vlm_config() -> Dict[str, Any]:
    """获取当前VLM配置"""
    provider = VLM_CONFIG["provider"]
    if provider not in VLM_CONFIG:
        raise ValueError(f"不支持的VLM提供商: {provider}")
    
    config = VLM_CONFIG[provider].copy()
    config["provider"] = provider
    return config


def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 检查LLM配置
    llm_provider = LLM_CONFIG["provider"]
    if llm_provider not in LLM_CONFIG:
        errors.append(f"无效的LLM提供商: {llm_provider}")
    else:
        llm_config = LLM_CONFIG[llm_provider]
        if not llm_config.get("api_key"):
            errors.append(f"缺少{llm_provider} API密钥")

    # 检查VLM配置
    vlm_provider = VLM_CONFIG["provider"]
    if vlm_provider not in VLM_CONFIG:
        errors.append(f"无效的VLM提供商: {vlm_provider}")
    else:
        vlm_config = VLM_CONFIG[vlm_provider]
        if not vlm_config.get("api_key"):
            errors.append(f"缺少{vlm_provider} VLM API密钥")
    
    # 检查规则文档目录
    rules_dir = AUDIT_CONFIG["rules_dir"]
    if not os.path.exists(rules_dir):
        errors.append(f"规则文档目录不存在: {rules_dir}")
    
    if errors:
        raise ValueError("配置验证失败:\n" + "\n".join(f"- {error}" for error in errors))
    
    return True


def get_test_config() -> Dict[str, Any]:
    """获取测试配置"""
    return TEST_CONFIG.copy()


def override_config_from_args(args_dict: Dict[str, Any]):
    """从命令行参数覆盖配置"""
    global DOCUMENT_CONFIG, TEST_CONFIG, SYSTEM_CONFIG

    # 覆盖数据路径
    if args_dict.get("data_dir"):
        TEST_CONFIG["data_paths"]["default_data_dir"] = args_dict["data_dir"]
        SYSTEM_CONFIG["paths"]["data_dir"] = args_dict["data_dir"]

    # 覆盖线程配置
    if args_dict.get("page_workers"):
        DOCUMENT_CONFIG["vlm_concurrency"]["page_level_workers"] = args_dict["page_workers"]
    if args_dict.get("doc_workers"):
        DOCUMENT_CONFIG["vlm_concurrency"]["document_level_workers"] = args_dict["doc_workers"]
    if args_dict.get("user_workers"):
        DOCUMENT_CONFIG["vlm_concurrency"]["user_level_workers"] = args_dict["user_workers"]

    # 覆盖缓存配置
    if args_dict.get("cache_dir"):
        DOCUMENT_CONFIG["cache"]["cache_dir"] = args_dict["cache_dir"]
        SYSTEM_CONFIG["paths"]["cache_dir"] = args_dict["cache_dir"]
    if args_dict.get("disable_cache"):
        DOCUMENT_CONFIG["cache"]["enabled"] = False

    # 覆盖输出配置
    if args_dict.get("output_dir"):
        AUDIT_CONFIG["output"]["default_dir"] = args_dict["output_dir"]
        SYSTEM_CONFIG["paths"]["output_dir"] = args_dict["output_dir"]

    # 覆盖API配置
    if args_dict.get("api_rate_limit"):
        DOCUMENT_CONFIG["vlm_concurrency"]["api_rate_limit"]["calls_per_minute"] = args_dict["api_rate_limit"]
    if args_dict.get("max_retries"):
        DOCUMENT_CONFIG["vlm_concurrency"]["retry_config"]["max_retries"] = args_dict["max_retries"]


def create_directories():
    """创建必要的目录"""
    dirs_to_create = [
        DOCUMENT_CONFIG["cache"]["cache_dir"],
        AUDIT_CONFIG["output"]["default_dir"],
        SYSTEM_CONFIG["temp_dir"],
        SYSTEM_CONFIG["paths"]["cache_dir"],
        SYSTEM_CONFIG["paths"]["output_dir"],
        SYSTEM_CONFIG["paths"]["log_dir"]
    ]

    for dir_path in dirs_to_create:
        if dir_path:  # 避免空路径
            os.makedirs(dir_path, exist_ok=True)


# 在导入时创建目录
create_directories()
