#!/usr/bin/env python3
"""
测试增强的材料清晰度评估功能
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.intelligent_audit_service import IntelligentAuditService
from src.tools.audit_report_generator import AuditReportGenerator


def test_enhanced_clarity_assessment():
    """测试增强的材料清晰度评估功能"""
    print("🧪 测试增强的材料清晰度评估功能")
    print("=" * 60)
    
    try:
        # 1. 初始化审核服务
        print("1. 初始化审核服务...")
        audit_service = IntelligentAuditService()
        
        # 2. 执行审核
        applicant_dir = "data0724/20160212111(河北石家庄）"
        print(f"2. 执行审核: {applicant_dir}")
        
        audit_result = audit_service.audit_applicant(applicant_dir)
        print("✅ 审核完成")
        
        # 3. 检查材料清晰度评估结果
        print("3. 检查增强的材料清晰度评估结果...")
        rule_analysis = audit_result.get("rule_based_analysis", {})
        
        if "error" in rule_analysis:
            print(f"❌ 审核分析失败: {rule_analysis.get('error')}")
            # 尝试解析原始响应
            raw_response = rule_analysis.get("raw_response", "")
            if raw_response:
                print("尝试从原始响应中提取清晰度信息...")
                if "clarity" in raw_response:
                    print("✅ 原始响应包含清晰度评估信息")
                else:
                    print("❌ 原始响应不包含清晰度评估信息")
            return False
        
        # 检查清晰度评估结构
        material_validity = rule_analysis.get("basic_info_verification", {}).get("material_validity", {})
        clarity = material_validity.get("clarity", {})
        
        print(f"   总体清晰度状态: {clarity.get('overall_status', '未知')}")
        print(f"   总体评估详情: {clarity.get('overall_details', '无')}")
        
        # 检查各文档清晰度分析
        doc_clarity = clarity.get("document_clarity_analysis", {})
        if doc_clarity:
            print("   各文档清晰度分析:")
            for doc_type, analysis in doc_clarity.items():
                status = analysis.get("status", "未知")
                details = analysis.get("details", "无详细信息")
                print(f"     - {doc_type}: {status} - {details}")
        else:
            print("   ❌ 缺少各文档清晰度分析")
        
        # 检查发现的清晰度问题
        clarity_issues = clarity.get("clarity_issues_found", [])
        if clarity_issues:
            print("   发现的清晰度问题:")
            for issue in clarity_issues:
                document = issue.get("document", "未知文档")
                issue_type = issue.get("issue_type", "未知问题")
                description = issue.get("description", "无描述")
                print(f"     - {document}: {issue_type} - {description}")
        else:
            print("   ✅ 未发现明显的清晰度问题")
        
        # 4. 分析VLM提取结果
        print("4. 分析VLM提取结果...")
        extraction_info = audit_result.get("extraction_cache_info", {})
        extraction_files = extraction_info.get("extraction_files", [])
        
        print(f"   处理的文档数量: {len(extraction_files)}")
        for file_info in extraction_files:
            doc_type = file_info.get("type", "未知类型")
            filename = file_info.get("filename", "未知文件")
            content_length = file_info.get("content_length", 0)
            print(f"   - {doc_type} ({filename}): {content_length} 字符")
        
        # 检查是否有明显的识别问题
        potential_issues = []
        for file_info in extraction_files:
            content_length = file_info.get("content_length", 0)
            if content_length < 50:  # 内容过短可能表示识别问题
                potential_issues.append(f"{file_info.get('type', '未知')} 内容过短")
            elif content_length > 50000:  # 内容过长可能表示重复识别
                potential_issues.append(f"{file_info.get('type', '未知')} 内容异常长")
        
        if potential_issues:
            print("   ⚠️ 发现潜在识别问题:")
            for issue in potential_issues:
                print(f"     - {issue}")
        else:
            print("   ✅ 未发现明显的识别问题")
        
        # 5. 生成审核报告
        print("5. 生成审核报告...")
        report_generator = AuditReportGenerator()
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f"results/enhanced_clarity_test_audit_result_{timestamp}.json"
        txt_filename = f"results/enhanced_clarity_test_audit_report_{timestamp}.txt"
        
        # 保存JSON结果
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(audit_result, f, ensure_ascii=False, indent=2)
        
        # 生成TXT报告
        txt_report = report_generator.generate_audit_report(audit_result)
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write(txt_report)
        
        # 检查报告中的清晰度评估内容
        if "材料清晰度" in txt_report:
            print("✅ 报告包含材料清晰度评估信息")
            # 提取清晰度相关内容
            lines = txt_report.split('\n')
            clarity_section = []
            in_clarity_section = False
            for line in lines:
                if "材料清晰度" in line:
                    in_clarity_section = True
                    clarity_section.append(line)
                elif in_clarity_section:
                    if line.strip() and not line.startswith(' '):
                        if "材料清晰度" not in line:
                            break
                    clarity_section.append(line)
            
            print("   清晰度评估内容:")
            for line in clarity_section[:10]:  # 显示前10行
                print(f"     {line}")
        else:
            print("❌ 报告不包含材料清晰度评估信息")
        
        print(f"✅ 报告生成成功:")
        print(f"   JSON: {json_filename}")
        print(f"   TXT:  {txt_filename}")
        
        # 6. 总结评估结果
        print("\n📊 增强的材料清晰度评估总结:")
        if clarity.get("overall_status"):
            print(f"   系统评估结果: {clarity.get('overall_status')}")
            print(f"   评估依据: {clarity.get('overall_details', '无')}")
            
            if doc_clarity:
                print(f"   文档分析数量: {len(doc_clarity)}")
                符合_count = sum(1 for analysis in doc_clarity.values() if analysis.get('status') == '符合')
                print(f"   清晰度符合: {符合_count}/{len(doc_clarity)}")
            
            if clarity_issues:
                print(f"   发现问题数量: {len(clarity_issues)}")
        else:
            print("   系统评估结果: 未获取到评估结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_enhanced_clarity_assessment()
    if success:
        print("\n🎉 增强的材料清晰度评估测试完成！")
    else:
        print("\n❌ 测试失败")
        sys.exit(1)
