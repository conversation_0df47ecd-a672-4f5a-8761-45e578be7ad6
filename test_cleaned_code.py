#!/usr/bin/env python3
"""
测试清理后的代码架构
验证无用代码清理后系统是否正常工作
"""

import os
import sys
import json
import time
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.core.intelligent_audit_service import IntelligentAuditService
from src.utils.timestamp_utils import generate_timestamp, extract_timestamp_from_audit_id


def test_cleaned_architecture():
    """测试清理后的架构"""
    print("🧹 测试清理后的代码架构")
    print("=" * 50)
    
    # 1. 检查IntelligentAuditService类的方法
    print("\n1. 检查IntelligentAuditService类的方法:")
    service = IntelligentAuditService()
    
    # 获取所有方法
    methods = [method for method in dir(service) if not method.startswith('__')]
    print(f"   总方法数: {len(methods)}")
    
    # 检查已删除的方法是否还存在
    removed_methods = [
        '_perform_basic_verification',
        '_perform_risk_assessment', 
        '_call_llm_for_verification',
        '_call_llm_for_assessment',
        '_call_llm_api',
        '_calculate_overall_verification_result',
        '_calculate_overall_risk',
        '_generate_final_assessment',
        '_generate_next_action'
    ]
    
    print("\n   已删除的方法检查:")
    for method in removed_methods:
        exists = hasattr(service, method)
        status = "❌ 仍存在" if exists else "✅ 已删除"
        print(f"   - {method}: {status}")
    
    # 检查保留的核心方法
    core_methods = [
        'audit_applicant',
        '_perform_rule_based_analysis',
        '_build_rule_based_analysis_prompt'
    ]
    
    print("\n   核心方法检查:")
    for method in core_methods:
        exists = hasattr(service, method)
        status = "✅ 存在" if exists else "❌ 缺失"
        print(f"   - {method}: {status}")
    
    return True


def test_audit_functionality():
    """测试审核功能是否正常"""
    print("\n2. 测试审核功能:")
    
    test_dir = "data0724/20160212111(河北石家庄）"
    if not os.path.exists(test_dir):
        print(f"   ❌ 测试目录不存在: {test_dir}")
        return False
    
    try:
        service = IntelligentAuditService()
        
        print(f"   📁 测试目录: {test_dir}")
        print("   🔄 开始审核...")
        
        start_time = time.time()
        result = service.audit_applicant(test_dir)
        end_time = time.time()
        
        print(f"   ⏱️  审核耗时: {end_time - start_time:.2f}秒")
        
        # 检查结果结构
        required_keys = ['audit_id', 'document_summary', 'rule_based_analysis', 'extraction_cache_info']
        missing_keys = [key for key in required_keys if key not in result]

        if missing_keys:
            print(f"   ❌ 缺少必要字段: {missing_keys}")
            return False

        print("   ✅ 审核功能正常")
        print(f"   📊 结果包含字段: {list(result.keys())}")

        # 检查审核分析结构
        analysis = result.get('rule_based_analysis', {})
        if analysis:
            print(f"   📋 审核分析包含: {list(analysis.keys())}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 审核功能测试失败: {e}")
        return False


def test_timestamp_functionality():
    """测试时间戳功能"""
    print("\n3. 测试时间戳功能:")
    
    # 测试时间戳生成
    timestamp = generate_timestamp()
    print(f"   📅 生成时间戳: {timestamp}")
    
    # 测试从audit_id提取时间戳
    audit_id = f"audit_{timestamp}"
    extracted = extract_timestamp_from_audit_id(audit_id)
    print(f"   🔍 从audit_id提取: {extracted}")
    
    if timestamp == extracted:
        print("   ✅ 时间戳功能正常")
        return True
    else:
        print("   ❌ 时间戳功能异常")
        return False


def test_file_generation():
    """测试文件生成功能"""
    print("\n4. 测试文件生成:")
    
    # 检查最新生成的文件
    results_dir = "results"
    if not os.path.exists(results_dir):
        print(f"   ❌ 结果目录不存在: {results_dir}")
        return False
    
    # 查找最新的审核结果文件
    json_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
    txt_files = [f for f in os.listdir(results_dir) if f.endswith('.txt')]
    
    print(f"   📄 JSON文件数量: {len(json_files)}")
    print(f"   📄 TXT文件数量: {len(txt_files)}")
    
    # 检查是否有带时间戳的文件
    timestamped_json = [f for f in json_files if '_20250729_' in f]
    timestamped_txt = [f for f in txt_files if '_20250729_' in f]
    
    print(f"   🕐 今日时间戳JSON文件: {len(timestamped_json)}")
    print(f"   🕐 今日时间戳TXT文件: {len(timestamped_txt)}")
    
    if timestamped_json and timestamped_txt:
        print("   ✅ 文件生成功能正常")
        
        # 显示最新文件
        latest_json = max(timestamped_json)
        latest_txt = max(timestamped_txt)
        print(f"   📋 最新JSON: {latest_json}")
        print(f"   📋 最新TXT: {latest_txt}")
        
        return True
    else:
        print("   ❌ 文件生成功能异常")
        return False


def main():
    """主测试函数"""
    print("🧪 代码清理后的系统测试")
    print("=" * 60)
    
    tests = [
        ("架构清理", test_cleaned_architecture),
        ("审核功能", test_audit_functionality), 
        ("时间戳功能", test_timestamp_functionality),
        ("文件生成", test_file_generation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！代码清理成功，系统运行正常。")
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
