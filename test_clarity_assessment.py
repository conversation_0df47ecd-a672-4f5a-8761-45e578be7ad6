#!/usr/bin/env python3
"""
测试材料清晰度智能评估功能
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.intelligent_audit_service import IntelligentAuditService
from src.tools.audit_report_generator import AuditReportGenerator

def test_clarity_assessment():
    """测试材料清晰度智能评估功能"""
    
    print("🧪 测试材料清晰度智能评估功能")
    print("=" * 60)
    
    # 1. 初始化服务
    print("1. 初始化审核服务...")
    audit_service = IntelligentAuditService()
    
    # 2. 执行审核
    applicant_dir = "data0724/20160212111(河北石家庄）"
    if not os.path.exists(applicant_dir):
        print(f"❌ 申请人目录不存在: {applicant_dir}")
        return False
    
    print(f"2. 执行审核: {applicant_dir}")
    try:
        audit_result = audit_service.audit_applicant(applicant_dir)
        print("✅ 审核完成")
    except Exception as e:
        print(f"❌ 审核失败: {e}")
        return False
    
    # 3. 检查材料清晰度评估结果
    print("3. 检查材料清晰度评估结果...")
    clarity = audit_result.get("rule_based_analysis", {}).get("basic_info_verification", {}).get("material_validity", {}).get("clarity", {})
    
    print(f"   清晰度状态: {clarity.get('status', '未知')}")
    print(f"   详细说明: {clarity.get('details', '无')}")
    
    # 4. 分析VLM提取结果，查找可能的识别错误
    print("4. 分析VLM提取结果...")
    extraction_cache_info = audit_result.get("extraction_cache_info", {})
    extraction_files = extraction_cache_info.get("extraction_files", [])
    
    print(f"   处理的文档数量: {len(extraction_files)}")
    
    # 检查是否有明显的识别问题
    potential_issues = []
    
    for doc in extraction_files:
        doc_type = doc.get("type", "未知类型")
        filename = doc.get("filename", "未知文件")
        content_length = doc.get("content_length", 0)
        
        print(f"   - {doc_type} ({filename}): {content_length} 字符")
        
        # 检查内容长度异常（可能表明识别问题）
        if content_length == 0:
            potential_issues.append(f"{doc_type}: 内容为空")
        elif content_length > 50000:  # 异常长的内容可能包含重复信息
            potential_issues.append(f"{doc_type}: 内容异常长({content_length}字符)")
    
    if potential_issues:
        print("   ⚠️  发现潜在识别问题:")
        for issue in potential_issues:
            print(f"     - {issue}")
    else:
        print("   ✅ 未发现明显的识别问题")
    
    # 5. 检查身份信息一致性（可能反映材料清晰度问题）
    print("5. 检查身份信息一致性...")
    identity_verification = audit_result.get("rule_based_analysis", {}).get("basic_info_verification", {}).get("information_consistency", {}).get("identity_verification", {})
    
    document_comparison = identity_verification.get("document_comparison", {})
    inconsistent_docs = []
    
    for comparison_type, result in document_comparison.items():
        if result.get("status") == "不符合":
            inconsistent_docs.append(f"{comparison_type}: {result.get('details', '无详细信息')}")
    
    if inconsistent_docs:
        print("   ⚠️  发现身份信息不一致（可能与材料清晰度有关）:")
        for inconsistency in inconsistent_docs:
            print(f"     - {inconsistency}")
    else:
        print("   ✅ 身份信息一致性检查通过")
    
    # 6. 生成报告并检查清晰度评估
    print("6. 生成审核报告...")
    try:
        report_generator = AuditReportGenerator()
        txt_content = report_generator.generate_audit_report(audit_result)
        
        # 检查报告中的清晰度评估内容
        if "材料清晰度" in txt_content or "清晰度" in txt_content:
            print("✅ 报告包含清晰度评估信息")
            
            # 提取清晰度相关内容
            lines = txt_content.split('\n')
            clarity_lines = [line for line in lines if '清晰度' in line]
            if clarity_lines:
                print("   清晰度评估内容:")
                for line in clarity_lines:
                    if line.strip():
                        print(f"     {line.strip()}")
        else:
            print("⚠️  报告中未找到清晰度评估信息")
        
        # 保存报告
        os.makedirs("results", exist_ok=True)
        
        # 保存JSON结果
        json_filename = "clarity_test_audit_result.json"
        json_path = f"results/{json_filename}"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(audit_result, f, ensure_ascii=False, indent=2)
        
        # 保存TXT报告
        txt_filename = "clarity_test_audit_report.txt"
        txt_path = f"results/{txt_filename}"
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write(txt_content)
        
        print(f"✅ 报告生成成功:")
        print(f"   JSON: {json_path}")
        print(f"   TXT:  {txt_path}")
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        return False
    
    # 7. 总结评估结果
    print("\n📊 材料清晰度评估总结:")
    print(f"   系统评估结果: {clarity.get('status', '未知')}")
    print(f"   评估依据: {clarity.get('details', '无')}")
    
    if potential_issues:
        print(f"   发现的技术问题: {len(potential_issues)}个")
    if inconsistent_docs:
        print(f"   身份信息不一致: {len(inconsistent_docs)}个")
    
    print("\n🎉 材料清晰度智能评估测试完成！")
    return True

if __name__ == "__main__":
    success = test_clarity_assessment()
    sys.exit(0 if success else 1)
