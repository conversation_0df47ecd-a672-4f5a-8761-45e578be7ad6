#!/usr/bin/env python3
"""
测试缓存控制功能
"""

import os
import sys
import logging
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.cache_manager import CacheManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_cache_exists():
    """检查缓存是否存在"""
    cache_manager = CacheManager()

    images_exist = os.path.exists(cache_manager.images_cache_dir) and os.listdir(cache_manager.images_cache_dir)
    extractions_exist = os.path.exists(cache_manager.extractions_cache_dir) and os.listdir(cache_manager.extractions_cache_dir)
    
    logger.info(f"图片缓存存在: {images_exist}")
    logger.info(f"提取结果缓存存在: {extractions_exist}")
    
    return images_exist, extractions_exist


def create_test_cache():
    """创建一些测试缓存"""
    logger.info("=== 创建测试缓存 ===")
    
    # 运行一个简单的提取测试来生成缓存
    result = subprocess.run([
        "python", "test_extraction_fix.py"
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        logger.info("✅ 测试缓存创建成功")
        return True
    else:
        logger.error(f"❌ 测试缓存创建失败: {result.stderr}")
        return False


def test_cache_control_commands():
    """测试缓存控制命令"""
    logger.info("\n=== 测试缓存控制命令 ===")
    
    # 测试1: 显示缓存统计
    logger.info("测试缓存统计命令...")
    result = subprocess.run([
        "python", "audit_cli.py", "cache", "--stats"
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        logger.info("✅ 缓存统计命令正常")
    else:
        logger.error(f"❌ 缓存统计命令失败: {result.stderr}")
        return False
    
    # 测试2: 清空图片缓存
    logger.info("测试清空图片缓存...")
    result = subprocess.run([
        "python", "audit_cli.py", "cache", "--clear-images"
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        logger.info("✅ 清空图片缓存命令正常")
        
        # 检查图片缓存是否被清空
        images_exist, _ = check_cache_exists()
        if not images_exist:
            logger.info("✅ 图片缓存已成功清空")
        else:
            logger.warning("⚠️  图片缓存可能未完全清空")
    else:
        logger.error(f"❌ 清空图片缓存命令失败: {result.stderr}")
        return False
    
    # 测试3: 清空提取结果缓存
    logger.info("测试清空提取结果缓存...")
    result = subprocess.run([
        "python", "audit_cli.py", "cache", "--clear-extractions"
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        logger.info("✅ 清空提取结果缓存命令正常")
        
        # 检查提取结果缓存是否被清空
        _, extractions_exist = check_cache_exists()
        if not extractions_exist:
            logger.info("✅ 提取结果缓存已成功清空")
        else:
            logger.warning("⚠️  提取结果缓存可能未完全清空")
    else:
        logger.error(f"❌ 清空提取结果缓存命令失败: {result.stderr}")
        return False
    
    return True


def test_cache_control_before_run():
    """测试运行前缓存控制"""
    logger.info("\n=== 测试运行前缓存控制 ===")
    
    # 先创建一些缓存
    if not create_test_cache():
        logger.error("无法创建测试缓存，跳过运行前缓存控制测试")
        return True
    
    # 测试运行前清空图片缓存
    logger.info("测试运行前清空图片缓存...")
    result = subprocess.run([
        "python", "audit_cli.py", "test", 
        "--config", "basic_config.yaml",
        "--clear-images-before-run"
    ], capture_output=True, text=True, timeout=60)
    
    if result.returncode == 0:
        logger.info("✅ 运行前清空图片缓存功能正常")
    else:
        logger.warning(f"⚠️  运行前清空图片缓存测试可能有问题: {result.stderr[:200]}")
    
    return True


def test_config_file_cache_control():
    """测试配置文件中的缓存控制"""
    logger.info("\n=== 测试配置文件缓存控制 ===")
    
    # 使用缓存控制配置文件
    logger.info("使用缓存控制配置文件运行测试...")
    result = subprocess.run([
        "python", "audit_cli.py", "test", 
        "--config", "config_templates/cache_control_config.yaml"
    ], capture_output=True, text=True, timeout=60)
    
    if result.returncode == 0:
        logger.info("✅ 配置文件缓存控制功能正常")
    else:
        logger.warning(f"⚠️  配置文件缓存控制测试可能有问题: {result.stderr[:200]}")
    
    return True


def main():
    """主测试函数"""
    logger.info("开始缓存控制功能测试")
    
    success = True
    
    # 检查初始缓存状态
    logger.info("=== 检查初始缓存状态 ===")
    check_cache_exists()
    
    # 测试1: 缓存控制命令
    try:
        if not test_cache_control_commands():
            success = False
    except Exception as e:
        logger.error(f"缓存控制命令测试失败: {e}")
        success = False
    
    # 测试2: 运行前缓存控制
    try:
        if not test_cache_control_before_run():
            success = False
    except Exception as e:
        logger.error(f"运行前缓存控制测试失败: {e}")
        success = False
    
    # 测试3: 配置文件缓存控制
    try:
        if not test_config_file_cache_control():
            success = False
    except Exception as e:
        logger.error(f"配置文件缓存控制测试失败: {e}")
        success = False
    
    # 总结
    logger.info("\n=== 测试总结 ===")
    if success:
        logger.info("🎉 缓存控制功能测试通过！")
        logger.info("✅ 支持命令行缓存控制")
        logger.info("✅ 支持运行前自动清理缓存")
        logger.info("✅ 支持配置文件缓存控制")
        logger.info("✅ 支持分别控制图片缓存和提取结果缓存")
    else:
        logger.error("❌ 缓存控制功能测试失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
