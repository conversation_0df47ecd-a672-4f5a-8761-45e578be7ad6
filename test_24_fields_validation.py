#!/usr/bin/env python3
"""
测试申请表24字段验证功能
"""

import json
import os
from datetime import datetime

def test_24_fields_validation():
    """测试24字段验证功能"""
    
    print("📋 测试申请表24字段验证功能")
    print("=" * 60)
    
    # 检查最新的审核结果
    results_dir = "results"
    if not os.path.exists(results_dir):
        print("❌ 结果目录不存在，请先运行审核")
        return False
    
    # 查找最新的JSON文件
    json_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
    if not json_files:
        print("❌ 未找到审核结果文件，请先运行审核")
        return False
    
    # 获取最新文件
    latest_file = max([os.path.join(results_dir, f) for f in json_files], key=os.path.getmtime)
    print(f"📄 检查文件: {os.path.basename(latest_file)}")
    
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            audit_result = json.load(f)
        
        # 提取申请表版式检查结果
        rule_analysis = audit_result.get("rule_based_analysis", {})
        basic_info = rule_analysis.get("basic_info_verification", {})
        material_validity = basic_info.get("material_validity", {})
        app_form = material_validity.get("application_form", {})
        format_integrity = app_form.get("format_integrity", {})
        
        print("\n📊 申请表版式检查结果:")
        print(f"   状态: {format_integrity.get('status', '未知')}")
        print(f"   详情: {format_integrity.get('details', '无详细信息')}")
        
        # 检查字段验证详情
        field_check = format_integrity.get("field_check", {})
        if field_check:
            print("\n🔍 字段验证详情:")
            print(f"   字段数量: {field_check.get('required_fields_count', '未知')}")
            
            missing_fields = field_check.get('missing_fields', [])
            if missing_fields:
                print(f"   ❌ 缺失字段 ({len(missing_fields)}个):")
                for field in missing_fields:
                    print(f"      • {field}")
            else:
                print("   ✅ 无缺失字段")
            
            extra_fields = field_check.get('extra_fields', [])
            if extra_fields:
                print(f"   ⚠️  多余字段 ({len(extra_fields)}个):")
                for field in extra_fields:
                    print(f"      • {field}")
            else:
                print("   ✅ 无多余字段")
            
            validation_details = field_check.get('field_validation_details', '')
            if validation_details:
                print(f"   📝 验证详情: {validation_details}")
        else:
            print("   ⚠️  未找到详细的字段检查信息")
        
        # 验证24字段要求
        print("\n📋 24字段要求验证:")
        required_24_fields = [
            "姓名", "性别", "出生日期", "民族", "籍贯", "政治面貌", "用工性质", "学历/学位",
            "毕业院校", "专业", "身份证号码", "银行业从业是否满两年", "现任岗位", "现任机构",
            "拟任机构", "申请单位", "申请人电话", "申请单位联系人", "联系人电话", "工作简历",
            "个人及家庭负债情况", "主要家庭成员及社会关系", "申请人承诺", "区县邮政分公司意见"
        ]
        
        print(f"   标准要求: {len(required_24_fields)} 个字段")
        
        if field_check:
            fields_count = field_check.get('required_fields_count', '未知')
            if '/' in str(fields_count):
                actual_count = fields_count.split('/')[0]
                try:
                    actual_count = int(actual_count)
                    if actual_count == 24:
                        print("   ✅ 字段数量符合要求")
                    else:
                        print(f"   ❌ 字段数量不符合要求: {actual_count}/24")
                except:
                    print(f"   ⚠️  字段数量格式异常: {fields_count}")
            else:
                print(f"   ⚠️  字段数量信息格式异常: {fields_count}")
        
        # 检查TXT报告中的字段信息
        print("\n📄 检查TXT报告中的字段信息:")
        txt_files = [f for f in os.listdir(results_dir) if f.endswith('.txt')]
        if txt_files:
            latest_txt = max([os.path.join(results_dir, f) for f in txt_files], key=os.path.getmtime)
            
            with open(latest_txt, 'r', encoding='utf-8') as f:
                report_content = f.read()
            
            # 检查是否包含字段检查信息
            if "字段检查:" in report_content:
                print("   ✅ TXT报告包含字段检查信息")
            else:
                print("   ⚠️  TXT报告未包含字段检查信息")
            
            if "缺失字段:" in report_content:
                print("   ✅ TXT报告包含缺失字段信息")
            
            if "多余字段:" in report_content:
                print("   ✅ TXT报告包含多余字段信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取审核结果失败: {e}")
        return False

def show_24_fields_requirements():
    """显示24字段要求"""
    
    print("\n📋 申请表必需的24个字段:")
    print("=" * 40)
    
    required_fields = [
        "1、姓名", "2、性别", "3、出生日期", "4、民族", "5、籍贯", "6、政治面貌",
        "7、用工性质", "8、学历/学位", "9、毕业院校", "10、专业", "11、身份证号码",
        "12、银行业从业是否满两年", "13、现任岗位", "14、现任机构", "15、拟任机构",
        "16、申请单位", "17、申请人电话", "18、申请单位联系人", "19、联系人电话",
        "20、工作简历", "21、个人及家庭负债情况", 
        "22、主要家庭成员及社会关系（包含关系、姓名、年龄、政治面貌、单位及职务子项）",
        "23、申请人承诺", "24、区县邮政分公司意见"
    ]
    
    for field in required_fields:
        print(f"   {field}")
    
    print(f"\n总计: {len(required_fields)} 个字段")
    print("⚠️  要求: 不能多也不能少")

if __name__ == "__main__":
    show_24_fields_requirements()
    
    print("\n" + "=" * 60)
    success = test_24_fields_validation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 24字段验证功能测试完成！")
        print("   系统现在可以检查申请表是否包含所有必需的24个字段")
    else:
        print("❌ 测试失败，请检查系统配置")
    
    print("\n💡 使用说明:")
    print("   运行审核命令后，系统会自动检查申请表的24个字段")
    print("   检查结果会显示在JSON和TXT报告的申请表版式检查部分")
