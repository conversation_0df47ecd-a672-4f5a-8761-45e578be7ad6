#!/usr/bin/env python3
"""
征信资格审核系统使用示例
展示各种使用场景和参数组合
"""

import os
import subprocess
import sys

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"示例: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*60}")
    
    # 这里只显示命令，不实际执行（避免重复处理）
    print("💡 执行此命令来运行示例")
    print(f"   {cmd}")


def main():
    """展示各种使用场景"""
    print("🚀 征信资格审核系统使用示例")
    
    # 基本使用场景
    examples = [
        {
            "cmd": "python audit.py",
            "desc": "基本使用 - 自动生成时间戳文件名"
        },
        {
            "cmd": "python audit.py --no_timestamp",
            "desc": "使用固定文件名（不带时间戳）"
        },
        {
            "cmd": "python audit.py --output my_result.json",
            "desc": "指定输出文件名"
        },
        {
            "cmd": "python audit.py --sample_root /path/to/applicant/folder",
            "desc": "处理自定义目录"
        },
        {
            "cmd": "python audit.py --resume_from previous_result.json",
            "desc": "从之前的结果文件继续处理"
        },
        {
            "cmd": "python audit.py --resume_from previous_result.json --force_reanalyze",
            "desc": "基于已有结果，强制重新生成审核结论"
        },
        {
            "cmd": "python audit.py --resume_from incomplete_result.json --output new_result.json",
            "desc": "从不完整结果继续，保存到新文件"
        }
    ]
    
    for example in examples:
        run_command(example["cmd"], example["desc"])
    
    # 使用场景说明
    print(f"\n{'='*60}")
    print("📋 使用场景详解")
    print(f"{'='*60}")
    
    scenarios = [
        {
            "scenario": "🆕 首次处理",
            "description": "第一次处理申请人资料",
            "command": "python audit.py",
            "result": "生成带时间戳的结果文件，如：credit_qualification_result_20250616_143000.json"
        },
        {
            "scenario": "⏸️ 中途中断后继续",
            "description": "处理过程中中断，想要继续处理",
            "command": "python audit.py --resume_from credit_qualification_result_20250616_143000.json",
            "result": "从中断点继续，跳过已完成的步骤"
        },
        {
            "scenario": "🔄 重新生成审核结果",
            "description": "图片识别和分析都完成了，但想重新生成审核结论",
            "command": "python audit.py --resume_from previous_result.json --force_reanalyze",
            "result": "保持图片识别结果，重新进行LLM分析和规则检查"
        },
        {
            "scenario": "📁 处理新的申请人",
            "description": "处理不同目录的申请人资料",
            "command": "python audit.py --sample_root /path/to/new/applicant",
            "result": "处理新目录，生成新的时间戳文件"
        },
        {
            "scenario": "🔧 调试和测试",
            "description": "开发调试时使用固定文件名",
            "command": "python audit.py --no_timestamp --output debug_result.json",
            "result": "使用固定文件名，便于调试"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['scenario']} {scenario['description']}")
        print(f"   命令: {scenario['command']}")
        print(f"   结果: {scenario['result']}")
    
    # 文件管理建议
    print(f"\n{'='*60}")
    print("📁 文件管理建议")
    print(f"{'='*60}")
    
    tips = [
        "💡 时间戳文件名便于版本管理，避免覆盖之前的结果",
        "💡 使用 --resume_from 可以从任何中断点继续处理",
        "💡 --force_reanalyze 适用于调整规则后重新评估",
        "💡 每次处理都会生成 JSON 和 Markdown 两种格式的报告",
        "💡 中间结果会实时保存，即使意外中断也不会丢失进度"
    ]
    
    for tip in tips:
        print(f"   {tip}")
    
    # 测试命令
    print(f"\n{'='*60}")
    print("🧪 测试和验证")
    print(f"{'='*60}")
    
    test_commands = [
        "python tests/test_all.py",
        "python tests/test_all.py --save_report test_report.json"
    ]
    
    for cmd in test_commands:
        print(f"   {cmd}")
    
    print(f"\n✅ 更多详细信息请查看 README.md")


if __name__ == "__main__":
    main()
