#!/usr/bin/env python3
"""
完整系统测试脚本
测试整个信贷资格审核系统的功能
支持命令行参数配置
"""

import os
import sys
import json
import logging
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.intelligent_audit_service import IntelligentAuditService
from src.utils.cache_manager import CacheManager
from config import override_config_from_args, get_test_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def find_test_data(data_dir: str = None):
    """查找测试数据"""
    # 获取测试配置
    test_config = get_test_config()

    # 使用指定的数据目录或配置中的默认目录
    if data_dir:
        base_dirs = [data_dir]
    else:
        base_dirs = [test_config["data_paths"]["default_data_dir"]] + test_config["data_paths"]["fallback_dirs"]

    # 构建测试目录列表
    test_dirs = []
    for base_dir in base_dirs:
        if os.path.exists(base_dir):
            # 检查是否直接包含PDF文件
            files = [f for f in os.listdir(base_dir) if f.endswith('.pdf')]
            if files:
                test_dirs.append(base_dir)

            # 检查子目录
            for item in os.listdir(base_dir):
                item_path = os.path.join(base_dir, item)
                if os.path.isdir(item_path):
                    sub_files = [f for f in os.listdir(item_path) if f.endswith('.pdf')]
                    if sub_files:
                        test_dirs.append(item_path)

    # 返回第一个找到的有效目录
    for test_dir in test_dirs:
        files = [f for f in os.listdir(test_dir) if f.endswith('.pdf')]
        if files:
            return test_dir, files

    return None, []


def test_document_extraction():
    """测试文档提取功能"""
    logger.info("=== 测试文档提取功能 ===")

    test_dir, files = find_test_data()
    if not test_dir:
        logger.warning("未找到测试数据，跳过文档提取测试")
        return False

    try:
        from src.extractors.universal_extractor import UniversalExtractor

        # 选择一个测试文件
        test_file = os.path.join(test_dir, files[0])
        logger.info(f"测试文件: {test_file}")

        # 创建提取器并提取文档内容
        extractor = UniversalExtractor(document_type="测试文档")
        logger.info("开始文档提取...")
        extraction_result = extractor.extract_from_pdf(test_file, use_cache=True)

        if "error" in extraction_result:
            logger.error(f"文档提取失败: {extraction_result['error']}")
            return False

        content_length = len(extraction_result.get('raw_content', ''))
        logger.info(f"提取成功: {content_length} 字符")

        if content_length > 0:
            logger.info("✅ 文档提取功能正常")
            return True
        else:
            logger.warning("⚠️  提取内容为空")
            return False

    except Exception as e:
        logger.error(f"文档提取测试异常: {e}")
        return False


def test_audit_analysis():
    """测试审核分析功能"""
    logger.info("=== 测试审核分析功能 ===")

    test_dir, files = find_test_data()
    if not test_dir:
        logger.warning("未找到测试数据，跳过审核分析测试")
        return False

    try:
        service = IntelligentAuditService()

        # 使用测试目录（包含多个文档）
        logger.info(f"测试目录: {test_dir}")

        # 执行完整审核
        logger.info("开始审核分析...")
        audit_result = service.audit_applicant(test_dir)

        if not audit_result:
            logger.error("审核分析失败")
            return False

        # 检查审核结果结构
        required_fields = [
            "extracted_data", "basic_verification", "risk_assessment",
            "final_assessment", "next_action"
        ]

        missing_fields = [field for field in required_fields if field not in audit_result]
        if missing_fields:
            logger.error(f"审核结果缺少必要字段: {missing_fields}")
            return False

        logger.info("✅ 审核结果结构完整")

        # 检查关键内容
        if audit_result.get("basic_verification", {}).get("overall_result", {}).get("result"):
            verification_result = audit_result["basic_verification"]["overall_result"]["result"]
            logger.info(f"基本验证结果: {verification_result}")

        if audit_result.get("risk_assessment", {}).get("overall_risk", {}).get("result"):
            risk_result = audit_result["risk_assessment"]["overall_risk"]["result"]
            logger.info(f"风险评估结果: {risk_result}")

        return True

    except Exception as e:
        logger.error(f"审核分析测试异常: {e}")
        return False


def test_cache_performance():
    """测试缓存性能"""
    logger.info("=== 测试缓存性能 ===")

    test_dir, files = find_test_data()
    if not test_dir:
        logger.warning("未找到测试数据，跳过缓存性能测试")
        return False

    try:
        from src.extractors.universal_extractor import UniversalExtractor

        test_file = os.path.join(test_dir, files[0])
        extractor = UniversalExtractor(document_type="测试文档")

        # 清空缓存
        cache_manager = CacheManager()
        cache_manager.clear_all_cache()

        # 第一次提取（创建缓存）
        logger.info("第一次提取（创建缓存）...")
        import time
        start_time = time.time()
        result1 = extractor.extract_from_pdf(test_file, use_cache=True)
        first_time = time.time() - start_time

        # 第二次提取（使用缓存）
        logger.info("第二次提取（使用缓存）...")
        start_time = time.time()
        result2 = extractor.extract_from_pdf(test_file, use_cache=True)
        second_time = time.time() - start_time

        # 比较性能
        if second_time < first_time * 0.5:
            speedup = first_time / second_time
            logger.info(f"✅ 缓存性能提升: {speedup:.1f}x ({first_time:.2f}s -> {second_time:.2f}s)")
            return True
        else:
            logger.warning(f"⚠️  缓存性能提升不明显: {first_time:.2f}s -> {second_time:.2f}s")
            return False

    except Exception as e:
        logger.error(f"缓存性能测试异常: {e}")
        return False


def test_system_integration():
    """测试系统集成"""
    logger.info("=== 测试系统集成 ===")

    test_dir, files = find_test_data()
    if not test_dir:
        logger.warning("未找到测试数据，跳过系统集成测试")
        return False

    try:
        service = IntelligentAuditService()

        # 测试完整目录处理
        logger.info(f"测试目录: {test_dir} ({len(files)} 个文档)")

        # 执行完整流程
        logger.info("执行完整审核流程...")
        audit_result = service.audit_applicant(test_dir)

        if not audit_result:
            logger.error("系统集成测试失败")
            return False

        # 保存结果
        result_file = "results/system_integration_test.json"
        os.makedirs("results", exist_ok=True)

        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(audit_result, f, ensure_ascii=False, indent=2)

        logger.info(f"✅ 系统集成测试通过，结果已保存到: {result_file}")

        # 显示关键信息
        extracted_data = audit_result.get("extracted_data", {})
        logger.info(f"处理文档数: {len(extracted_data)}")

        basic_verification = audit_result.get("basic_verification", {})
        if basic_verification:
            logger.info(f"基本验证: {basic_verification.get('overall_result', {}).get('result', '未知')}")

        return True

    except Exception as e:
        logger.error(f"系统集成测试异常: {e}")
        return False


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="完整系统测试脚本")

    # 数据配置
    parser.add_argument("--data-dir", help="测试数据目录路径")
    parser.add_argument("--cache-dir", help="缓存目录路径")
    parser.add_argument("--output-dir", help="输出目录路径")

    # 并发配置
    parser.add_argument("--page-workers", type=int, help="页面级并发线程数")
    parser.add_argument("--doc-workers", type=int, help="文档级并发线程数")
    parser.add_argument("--user-workers", type=int, help="用户级并发线程数")

    # 测试配置
    parser.add_argument("--with-llm", action="store_true", help="启用LLM测试")
    parser.add_argument("--with-vlm", action="store_true", help="启用VLM测试")
    parser.add_argument("--disable-cache", action="store_true", help="禁用缓存")
    parser.add_argument("--max-test-files", type=int, default=3, help="最大测试文件数")

    # API配置
    parser.add_argument("--api-rate-limit", type=int, help="API调用频率限制（次/分钟）")
    parser.add_argument("--max-retries", type=int, help="最大重试次数")

    # 配置文件
    parser.add_argument("--config", help="配置文件路径")

    # 其他选项
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")

    return parser.parse_args()


def main():
    """主测试函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 应用配置文件
    if args.config:
        from src.utils.config_manager import ConfigManager
        config_manager = ConfigManager()
        if not config_manager.apply_config_file(args.config):
            logger.error("配置文件应用失败")
            return False

    # 应用命令行参数覆盖
    args_dict = {k: v for k, v in vars(args).items() if v is not None}
    override_config_from_args(args_dict)

    logger.info("开始完整系统测试")

    # 检查测试数据
    test_dir, files = find_test_data(args.data_dir)
    if not test_dir:
        logger.error("❌ 未找到测试数据")
        logger.info("请确保以下目录之一存在且包含PDF文件:")
        test_config = get_test_config()
        for dir_path in [test_config["data_paths"]["default_data_dir"]] + test_config["data_paths"]["fallback_dirs"]:
            logger.info(f"  - {dir_path}")
        return False

    logger.info(f"找到测试数据: {test_dir} ({len(files)} 个PDF文件)")

    # 限制测试文件数量
    if len(files) > args.max_test_files:
        logger.info(f"限制测试文件数量为 {args.max_test_files} 个")

    test_results = []

    # 测试1: 文档提取
    test_results.append(("文档提取功能", test_document_extraction()))

    # 测试2: 缓存性能
    test_results.append(("缓存性能", test_cache_performance()))

    # 测试3: 审核分析（可选，需要API）
    if args.with_llm:
        test_results.append(("审核分析功能", test_audit_analysis()))
        test_results.append(("系统集成", test_system_integration()))
    else:
        logger.info("跳过LLM测试（使用 --with-llm 参数启用）")
    
    # 汇总结果
    logger.info("="*50)
    logger.info("测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 完整系统测试通过！")
        
        # 显示缓存状态
        logger.info("\n" + "="*50)
        cache_manager = CacheManager()
        cache_manager.print_cache_report()
        
        return True
    else:
        logger.warning("⚠️  部分测试失败，请检查系统配置。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
