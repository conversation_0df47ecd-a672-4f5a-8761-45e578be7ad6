#!/usr/bin/env python3
"""
最终测试24字段验证功能
"""

import json
import os
import subprocess
from datetime import datetime

def run_audit_and_test():
    """运行审核并测试24字段验证"""
    
    print("🚀 运行最终24字段验证测试")
    print("=" * 60)
    
    # 运行审核
    print("📋 正在运行审核...")
    try:
        result = subprocess.run([
            "python", "audit_cli.py", "audit", 
            "data0724/20160212111(河北石家庄）", 
            "--output-dir", "results", 
            "--verbose"
        ], capture_output=True, text=True, timeout=180)
        
        if result.returncode != 0:
            print(f"❌ 审核失败: {result.stderr}")
            return False
            
        print("✅ 审核完成")
        
    except subprocess.TimeoutExpired:
        print("❌ 审核超时")
        return False
    except Exception as e:
        print(f"❌ 审核异常: {e}")
        return False
    
    # 查找最新的结果文件
    results_dir = "results"
    json_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
    if not json_files:
        print("❌ 未找到审核结果文件")
        return False
    
    latest_json = max([os.path.join(results_dir, f) for f in json_files], key=os.path.getmtime)
    
    # 分析结果
    print(f"\n📄 分析结果文件: {os.path.basename(latest_json)}")
    
    try:
        with open(latest_json, 'r', encoding='utf-8') as f:
            audit_result = json.load(f)
        
        # 检查是否有JSON解析错误
        rule_analysis = audit_result.get("rule_based_analysis", {})
        if "error" in rule_analysis:
            print("❌ JSON解析失败，检查raw_response")
            raw_response = rule_analysis.get("raw_response", "")
            if "format_integrity" in raw_response:
                print("✅ raw_response中包含字段检查信息")
            return False
        
        # 提取字段检查结果
        basic_info = rule_analysis.get("basic_info_verification", {})
        material_validity = basic_info.get("material_validity", {})
        app_form = material_validity.get("application_form", {})
        format_integrity = app_form.get("format_integrity", {})
        field_check = format_integrity.get("field_check", {})
        
        print("\n🔍 字段验证结果:")
        print(f"   状态: {format_integrity.get('status', '未知')}")
        print(f"   详情: {format_integrity.get('details', '无详细信息')}")
        print(f"   字段计数: {field_check.get('required_fields_count', '未知')}")
        
        missing_fields = field_check.get('missing_fields', [])
        extra_fields = field_check.get('extra_fields', [])
        
        print(f"\n📊 字段分析:")
        print(f"   缺失字段 ({len(missing_fields)}个):")
        for field in missing_fields:
            print(f"      ❌ {field}")
        
        print(f"   多余字段 ({len(extra_fields)}个):")
        for field in extra_fields:
            print(f"      ⚠️  {field}")
        
        # 验证预期结果
        print(f"\n✅ 预期验证:")
        
        # 根据申请表内容，预期应该是：
        # 缺失：个人及家庭负债情况（申请表中只有"个人及家庭情况"）
        # 多余：市(区)邮政分公司金融业务部意见, 市(区)邮政分公司人力资源部意见
        # 区县邮政分公司意见应该能匹配到"县(区)邮政分公司意见"
        
        expected_missing = ["个人及家庭负债情况"]
        expected_extra = ["市(区)邮政分公司金融业务部意见", "市(区)邮政分公司人力资源部意见"]
        
        missing_correct = set(missing_fields) == set(expected_missing)
        extra_correct = set(extra_fields) == set(expected_extra)
        
        print(f"   缺失字段识别: {'✅ 正确' if missing_correct else '❌ 错误'}")
        if not missing_correct:
            print(f"      预期: {expected_missing}")
            print(f"      实际: {missing_fields}")
        
        print(f"   多余字段识别: {'✅ 正确' if extra_correct else '❌ 错误'}")
        if not extra_correct:
            print(f"      预期: {expected_extra}")
            print(f"      实际: {extra_fields}")
        
        # 检查TXT报告
        txt_files = [f for f in os.listdir(results_dir) if f.endswith('.txt')]
        if txt_files:
            latest_txt = max([os.path.join(results_dir, f) for f in txt_files], key=os.path.getmtime)
            
            with open(latest_txt, 'r', encoding='utf-8') as f:
                report_content = f.read()
            
            print(f"\n📄 TXT报告检查:")
            has_field_check = "字段检查:" in report_content
            has_missing = "缺失字段:" in report_content
            has_extra = "多余字段:" in report_content
            
            print(f"   包含字段检查: {'✅' if has_field_check else '❌'}")
            print(f"   包含缺失字段: {'✅' if has_missing else '❌'}")
            print(f"   包含多余字段: {'✅' if has_extra else '❌'}")
        
        overall_success = missing_correct and extra_correct
        return overall_success
        
    except Exception as e:
        print(f"❌ 分析结果失败: {e}")
        return False

def show_application_form_analysis():
    """显示申请表内容分析"""
    
    print("\n📋 申请表内容分析")
    print("=" * 40)
    
    print("根据提供的申请表内容，实际包含的字段：")
    actual_fields = [
        "姓名", "性别", "出生日期", "民族", "籍贯", "政治面貌",
        "用工性质", "学历/学位", "毕业院校", "专业", "身份证号码",
        "银行业从业是否满两年", "现任岗位", "现任机构", "拟任机构",
        "申请单位", "申请人电话", "申请单位联系人", "联系人电话",
        "工作简历", "个人及家庭情况",  # 注意：不是"个人及家庭负债情况"
        "主要家庭成员及社会关系", "申请人承诺", 
        "县(区)邮政分公司意见",  # 标准字段的变体
        "市(区)邮政分公司金融业务部意见",  # 多余字段
        "市(区)邮政分公司人力资源部意见"   # 多余字段
    ]
    
    print(f"\n实际字段总数: {len(actual_fields)}")
    
    print("\n🔍 字段对比分析:")
    print("   ✅ 符合标准的字段: 23个")
    print("   ❌ 缺失的字段: 1个 (个人及家庭负债情况)")
    print("   ⚠️  多余的字段: 2个 (市级审批意见)")
    print("   📝 字段变体: 县(区)邮政分公司意见 ≈ 区县邮政分公司意见")

if __name__ == "__main__":
    show_application_form_analysis()
    
    print("\n" + "=" * 60)
    success = run_audit_and_test()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 24字段验证功能测试成功！")
        print("   ✅ 系统正确识别了缺失和多余的字段")
        print("   ✅ 字段匹配规则工作正常")
        print("   ✅ JSON和TXT报告都包含完整的字段信息")
    else:
        print("❌ 测试未完全成功，需要进一步调整")
    
    print("\n💡 总结:")
    print("   系统现在可以准确检查申请表的24个标准字段")
    print("   能够识别字段名称的合理变体（如区县/县区）")
    print("   严格区分相似但不同的字段（如个人情况 vs 负债情况）")
    print("   准确识别多余的审批级别字段")
