#!/usr/bin/env python3
"""
测试复杂JSON结构的生成
"""

import sys
from pathlib import Path
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.llm_client import LLMClient

def test_complex_json():
    """测试复杂JSON结构生成"""
    print("=== 测试复杂JSON结构生成 ===")
    
    # 初始化LLM客户端
    llm_client = LLMClient()
    
    # 复杂的JSON测试
    user_message = """
请分析以下信息并以JSON格式返回：
- 姓名：张三
- 年龄：25岁
- 职业：工程师
- 教育背景：本科，计算机科学
- 工作经验：3年
- 技能：Python, Java, 数据库
- 项目经验：电商系统开发，用户管理系统

请按照以下JSON结构返回：
{
    "personal_info": {
        "name": "姓名",
        "age": "年龄",
        "occupation": "职业"
    },
    "education": {
        "level": "学历",
        "major": "专业"
    },
    "work_experience": {
        "years": "工作年限",
        "skills": ["技能列表"],
        "projects": ["项目列表"]
    },
    "assessment": {
        "overall_rating": "综合评价",
        "strengths": ["优势列表"],
        "recommendations": ["建议列表"]
    }
}
"""
    
    system_message = "你是一个专业的人力资源分析师，请将用户提供的信息分析并以JSON格式返回。"
    
    print("测试复杂JSON结构...")
    print("\n" + "="*50)
    
    try:
        result = llm_client.chat_with_json(
            user_message=user_message,
            system_message=system_message
        )
        
        print("返回结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        if "error" in result:
            print(f"\n❌ JSON解析失败")
            print(f"原始响应: {result.get('raw_response', '')[:1000]}...")
            
            # 尝试手动清理JSON
            raw_response = result.get('raw_response', '')
            if raw_response.startswith("```json"):
                cleaned = raw_response[7:]
                if cleaned.endswith("```"):
                    cleaned = cleaned[:-3]
                cleaned = cleaned.strip()
                
                try:
                    parsed = json.loads(cleaned)
                    print(f"\n✅ 手动清理后JSON解析成功")
                    print(json.dumps(parsed, ensure_ascii=False, indent=2))
                except json.JSONDecodeError as e:
                    print(f"\n❌ 手动清理后仍然解析失败: {e}")
        else:
            print(f"\n✅ JSON解析成功")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_complex_json()
