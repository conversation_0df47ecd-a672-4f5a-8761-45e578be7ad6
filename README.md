# 智能审核系统 v2.0

基于LLM和VLM的代理营业机构负责人任职资格智能审核系统。采用全新架构，保持规则文档原文，支持补充材料，提供更准确的审核结果。

## 🚀 核心特性

### ✨ **全新架构特点**
- **原文保持**：规则文档完整传递给LLM，无信息丢失
- **补充材料支持**：自动识别谈话记录、岗位证书、工作证明等
- **智能分类**：基于文件名和内容的文档自动分类
- **LLM驱动**：使用大语言模型进行智能审核和风险评估
- **多模型支持**：支持OpenAI、DeepSeek、Qwen、本地模型

### 📋 **审核流程**
```
文档扫描 → 智能分类 → VLM提取 → LLM审核 → 结果生成
    ↓         ↓         ↓        ↓        ↓
  PDF文档   6类标准   图片转文本  规则匹配  JSON/Word/MD
           +补充材料
```

### 📁 **支持的文档类型**

**标准文档（必需）：**
1. 申请表
2. 身份证复印件  
3. 征信报告
4. 教育背景
5. 邮政履历
6. 执行网公开信息

**补充材料（可选）：**
- 谈话记录
- 岗位证书
- 工作证明
- 其他相关材料

## 🛠️ 安装配置

### 1. 环境要求
```bash
Python 3.8+
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. API配置

**✅ API密钥已配置完成**
- **DeepSeek V3**: `***********************************`
- **Qwen2.5-72B** (硅基智能): `sk-plpykhaoetmlvsvzoffnljxsgykhlpwwleewhugruiugglxc`
- **Qwen2.5-VL-72B** (硅基智能): `sk-plpykhaoetmlvsvzoffnljxsgykhlpwwleewhugruiugglxc`

**切换LLM提供商**
```bash
# 使用DeepSeek（默认）
export LLM_PROVIDER=deepseek

# 使用Qwen
export LLM_PROVIDER=qwen
```

**或直接修改config.py中的provider字段**

### 4. 准备规则文档
确保 `规则文档/` 目录包含：
- 审核报告规则(初稿）0725.docx
- 人行征信系统个人信用信息分类标准.docx  
- 关于明确代理营业机构负责人任职资格征信审核要点的通知.docx

## 🎯 使用方法

### 单个申请人审核
```bash
# 基本审核
python intelligent_audit.py data0724/申请人目录

# 生成完整报告
python intelligent_audit.py data0724/申请人目录 --generate-word --generate-markdown

# 指定输出目录
python intelligent_audit.py data0724/申请人目录 --output-dir my_results
```

### 批量审核
```bash
# 批量处理整个目录
python intelligent_audit.py batch data0724/ --generate-reports

# 指定输出目录
python intelligent_audit.py batch data0724/ --output-dir batch_results
```

### 测试系统
```bash
# 运行完整测试
python test_intelligent_audit.py

# 测试LLM连接
python -m src.utils.llm_client
```

## 📊 输出格式

### JSON结果
```json
{
  "audit_id": "audit_20250125_143022",
  "document_summary": {
    "total_documents": 8,
    "standard_documents": {"count": 6},
    "supplementary_materials": {"count": 2}
  },
  "basic_verification": {
    "overall_result": {"result": "符合"},
    "verification_items": {...}
  },
  "risk_assessment": {
    "overall_risk": {"result": "符合"},
    "assessment_items": {...}
  },
  "final_assessment": {...},
  "next_action": {...}
}
```

### Word报告
- 标准化格式
- 表格形式展示风险评估
- 符合业务规范

### Markdown报告  
- 便于查看和分享
- 结构化展示
- 支持在线预览

## 🔧 系统架构

```
src/
├── core/                          # 核心组件
│   ├── intelligent_audit_service.py    # 主审核服务
│   ├── document_based_rules_manager.py # 规则管理器
│   └── enhanced_document_manager.py    # 文档管理器
├── extractors/                    # 文档提取器
│   ├── base_extractor.py             # 基础提取器
│   ├── application_form_extractor.py # 申请表提取器
│   └── ...                          # 其他专用提取器
├── tools/                         # 转换工具
│   ├── json_to_word.py              # JSON转Word
│   └── json_to_markdown.py          # JSON转Markdown
└── utils/                         # 工具类
    └── llm_client.py                # LLM客户端
```

## ⚙️ 高级配置

### 自定义LLM参数
```python
# config.py
LLM_CONFIG = {
    "openai": {
        "temperature": 0.1,    # 降低随机性
        "max_tokens": 2000,    # 最大输出长度
        "model": "gpt-4"       # 模型选择
    }
}
```

### 文档处理配置
```python
DOCUMENT_CONFIG = {
    "pdf_to_image": {
        "dpi": 200,           # 图片质量
        "thread_count": 4     # 并发线程数
    },
    "cache": {
        "enabled": True,      # 启用缓存
        "cache_ttl_hours": 24 # 缓存有效期
    }
}
```

## 🔍 故障排除

### 常见问题

**1. LLM连接失败**
```bash
# 检查配置
python -m src.utils.llm_client

# 检查网络和API密钥
```

**2. 文档提取失败**
```bash
# 检查PDF文件是否损坏
# 检查VLM API配置
```

**3. 规则文档缺失**
```bash
# 确保规则文档目录完整
ls 规则文档/
```

### 日志查看
```bash
# 查看详细日志
tail -f audit.log

# 启用调试模式
python intelligent_audit.py data0724/申请人目录 --verbose
```

## 🆕 版本更新

### v2.0 主要变更
- ✅ 全新的基于原文的规则管理架构
- ✅ 支持补充材料自动识别和处理
- ✅ 统一的LLM客户端，支持多种模型
- ✅ 完整的配置管理系统
- ✅ 清理了所有无用代码
- ✅ 新的测试和使用接口

### 从v1.0迁移
旧版本的代码已完全重构，请使用新的接口：
- 旧：`python audit.py` → 新：`python intelligent_audit.py`
- 旧：硬编码规则解析 → 新：原文传递给LLM
- 旧：仅支持6类文档 → 新：支持补充材料

## 📞 技术支持

- 查看日志文件：`audit.log`
- 运行测试脚本：`python test_intelligent_audit.py`
- 检查配置：`python -c "from config import validate_config; validate_config()"`
