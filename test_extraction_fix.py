#!/usr/bin/env python3
"""
测试提取功能修复 - 验证不再有重复的 raw_text 字段
"""

import os
import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.extractors.universal_extractor import UniversalExtractor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_extraction_output():
    """测试提取输出格式"""
    logger.info("=== 测试提取输出格式 ===")
    
    # 查找测试文件
    test_dirs = ["data0724", "data", "test_data"]
    test_file = None
    
    for dir_name in test_dirs:
        if os.path.exists(dir_name):
            for item in os.listdir(dir_name):
                item_path = os.path.join(dir_name, item)
                if os.path.isdir(item_path):
                    pdf_files = [f for f in os.listdir(item_path) if f.endswith('.pdf')]
                    if pdf_files:
                        test_file = os.path.join(item_path, pdf_files[0])
                        break
            if test_file:
                break
    
    if not test_file:
        logger.error("未找到测试PDF文件")
        return False
    
    logger.info(f"使用测试文件: {test_file}")
    
    # 创建提取器
    extractor = UniversalExtractor()
    
    # 提取文档
    result = extractor.extract_from_pdf(test_file, use_cache=False)
    
    # 检查结果格式
    logger.info("=== 提取结果分析 ===")
    logger.info(f"结果类型: {type(result)}")
    logger.info(f"结果键: {list(result.keys())}")
    
    # 检查是否有重复字段
    has_raw_content = "raw_content" in result
    has_raw_text = "raw_text" in result
    
    logger.info(f"包含 raw_content: {has_raw_content}")
    logger.info(f"包含 raw_text: {has_raw_text}")
    
    if has_raw_content and has_raw_text:
        logger.error("❌ 发现重复字段！同时包含 raw_content 和 raw_text")
        
        # 比较内容是否相同
        content1 = result["raw_content"]
        content2 = result["raw_text"]
        
        if content1 == content2:
            logger.error("两个字段内容完全相同，确认是重复字段")
        else:
            logger.warning("两个字段内容不同，可能有不同用途")
        
        return False
    
    elif has_raw_content and not has_raw_text:
        logger.info("✅ 正确！只包含 raw_content 字段")
        
        # 显示内容预览
        content = result["raw_content"]
        preview = content[:200] + "..." if len(content) > 200 else content
        logger.info(f"内容预览: {preview}")
        
        return True
    
    elif has_raw_text and not has_raw_content:
        logger.warning("⚠️  只包含 raw_text 字段，应该是 raw_content")
        return False
    
    else:
        logger.error("❌ 两个字段都不存在")
        return False


def test_cache_format():
    """测试缓存文件格式"""
    logger.info("\n=== 测试缓存文件格式 ===")
    
    cache_dir = "cache/extractions"
    if not os.path.exists(cache_dir):
        logger.info("缓存目录不存在，跳过缓存格式测试")
        return True
    
    cache_files = [f for f in os.listdir(cache_dir) if f.endswith('.json')]
    
    if not cache_files:
        logger.info("没有缓存文件，跳过缓存格式测试")
        return True
    
    logger.info(f"发现 {len(cache_files)} 个缓存文件")
    
    # 检查第一个缓存文件
    cache_file = os.path.join(cache_dir, cache_files[0])
    
    try:
        with open(cache_file, 'r', encoding='utf-8') as f:
            cache_data = json.load(f)
        
        logger.info(f"缓存文件键: {list(cache_data.keys())}")
        
        has_raw_content = "raw_content" in cache_data
        has_raw_text = "raw_text" in cache_data
        
        logger.info(f"缓存包含 raw_content: {has_raw_content}")
        logger.info(f"缓存包含 raw_text: {has_raw_text}")
        
        if has_raw_content and has_raw_text:
            logger.error("❌ 缓存文件包含重复字段")
            return False
        elif has_raw_content and not has_raw_text:
            logger.info("✅ 缓存格式正确")
            return True
        else:
            logger.warning("⚠️  缓存格式异常")
            return False
    
    except Exception as e:
        logger.error(f"读取缓存文件失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始提取功能修复验证")
    
    success = True
    
    # 测试1: 提取输出格式
    try:
        if not test_extraction_output():
            success = False
    except Exception as e:
        logger.error(f"提取输出格式测试失败: {e}")
        success = False
    
    # 测试2: 缓存文件格式
    try:
        if not test_cache_format():
            success = False
    except Exception as e:
        logger.error(f"缓存文件格式测试失败: {e}")
        success = False
    
    # 总结
    logger.info("\n=== 测试总结 ===")
    if success:
        logger.info("🎉 提取功能修复验证通过！")
        logger.info("✅ 不再有重复的 raw_text 字段")
        logger.info("✅ 统一使用 raw_content 字段")
    else:
        logger.error("❌ 提取功能修复验证失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
