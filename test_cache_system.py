#!/usr/bin/env python3
"""
缓存系统测试脚本
测试PDF图片缓存和VLM结果缓存功能
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.extractors.universal_extractor import UniversalExtractor
from src.utils.cache_manager import CacheManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_pdf_image_cache():
    """测试PDF图片缓存功能"""
    logger.info("=== 测试PDF图片缓存 ===")
    
    # 查找测试PDF文件
    test_dirs = [
        "data0724/20080766971（山西太原）",
        "data0724/20090302521（天津）",
        "data0724/20111212991（北京）",
        "data0724/20160212111(河北石家庄）"
    ]
    
    test_file = None
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            for file in os.listdir(test_dir):
                if file.endswith('.pdf'):
                    test_file = os.path.join(test_dir, file)
                    break
            if test_file:
                break
    
    if not test_file:
        logger.warning("未找到测试PDF文件，跳过图片缓存测试")
        return False
    
    logger.info(f"使用测试文件: {test_file}")
    
    try:
        # 创建提取器
        extractor = UniversalExtractor(document_type="测试文档")
        
        # 第一次提取（应该创建缓存）
        logger.info("第一次提取（创建缓存）...")
        import time
        start_time = time.time()
        result1 = extractor.extract_from_pdf(test_file, use_cache=True)
        first_time = time.time() - start_time
        
        if "error" in result1:
            logger.error(f"第一次提取失败: {result1['error']}")
            return False
        
        logger.info(f"第一次提取完成，耗时: {first_time:.2f}秒")
        logger.info(f"提取内容长度: {len(result1.get('raw_content', ''))}")
        
        # 第二次提取（应该使用缓存）
        logger.info("第二次提取（使用缓存）...")
        start_time = time.time()
        result2 = extractor.extract_from_pdf(test_file, use_cache=True)
        second_time = time.time() - start_time
        
        if "error" in result2:
            logger.error(f"第二次提取失败: {result2['error']}")
            return False
        
        logger.info(f"第二次提取完成，耗时: {second_time:.2f}秒")
        
        # 比较结果
        if result1.get('raw_content') == result2.get('raw_content'):
            logger.info("✅ 缓存结果一致")
        else:
            logger.warning("⚠️  缓存结果不一致")
        
        # 检查性能提升
        if second_time < first_time * 0.5:  # 缓存应该至少快50%
            speedup = first_time / second_time
            logger.info(f"✅ 缓存性能提升: {speedup:.1f}x")
        else:
            logger.warning(f"⚠️  缓存性能提升不明显: {first_time:.2f}s -> {second_time:.2f}s")
        
        return True
        
    except Exception as e:
        logger.error(f"图片缓存测试异常: {e}")
        return False


def test_cache_management():
    """测试缓存管理功能"""
    logger.info("=== 测试缓存管理 ===")
    
    try:
        manager = CacheManager()
        
        # 获取缓存统计
        stats = manager.get_cache_stats()
        logger.info(f"当前缓存统计:")
        logger.info(f"  总大小: {stats['total_size_mb']} MB")
        logger.info(f"  总文件数: {stats['total_files']}")
        
        if stats["images_cache"]:
            img_stats = stats["images_cache"]
            logger.info(f"  图片缓存: {img_stats['size_mb']} MB, {img_stats['subdirectories']} 个PDF")
        
        if stats["extractions_cache"]:
            ext_stats = stats["extractions_cache"]
            logger.info(f"  提取缓存: {ext_stats['size_mb']} MB, {ext_stats['file_count']} 个文件")
        
        # 验证缓存完整性
        issues = manager.validate_cache_integrity()
        total_issues = sum(len(issue_list) for issue_list in issues.values())
        
        if total_issues == 0:
            logger.info("✅ 缓存完整性检查通过")
        else:
            logger.warning(f"⚠️  发现 {total_issues} 个缓存问题")
            for issue_type, issue_list in issues.items():
                if issue_list:
                    logger.warning(f"  {issue_type}: {len(issue_list)} 个问题")
        
        return True
        
    except Exception as e:
        logger.error(f"缓存管理测试异常: {e}")
        return False


def test_cache_without_vlm():
    """测试缓存功能（不调用VLM）"""
    logger.info("=== 测试缓存功能（仅PDF转图片）===")
    
    # 查找测试PDF文件
    test_dirs = [
        "data0724/20080766971（山西太原）",
        "data0724/20090302521（天津）"
    ]
    
    test_file = None
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            for file in os.listdir(test_dir):
                if file.endswith('.pdf'):
                    test_file = os.path.join(test_dir, file)
                    break
            if test_file:
                break
    
    if not test_file:
        logger.warning("未找到测试PDF文件")
        return False
    
    try:
        from src.utils.pdf_converter import PDFConverter
        
        # 创建PDF转换器
        converter = PDFConverter()
        
        # 第一次转换（创建缓存）
        logger.info("第一次PDF转图片（创建缓存）...")
        import time
        start_time = time.time()
        images1 = converter.convert_pdf_to_images(
            pdf_path=test_file,
            return_dict=True,
            return_base64=True,
            use_cache=True
        )
        first_time = time.time() - start_time
        
        if not images1:
            logger.error("第一次转换失败")
            return False
        
        logger.info(f"第一次转换完成，耗时: {first_time:.2f}秒，页数: {len(images1)}")
        
        # 第二次转换（使用缓存）
        logger.info("第二次PDF转图片（使用缓存）...")
        start_time = time.time()
        images2 = converter.convert_pdf_to_images(
            pdf_path=test_file,
            return_dict=True,
            return_base64=True,
            use_cache=True
        )
        second_time = time.time() - start_time
        
        if not images2:
            logger.error("第二次转换失败")
            return False
        
        logger.info(f"第二次转换完成，耗时: {second_time:.2f}秒")
        
        # 比较结果
        if len(images1) == len(images2):
            logger.info("✅ 缓存页数一致")
        else:
            logger.warning("⚠️  缓存页数不一致")
        
        # 检查性能提升
        if second_time < first_time * 0.3:  # 图片缓存应该非常快
            speedup = first_time / second_time
            logger.info(f"✅ 图片缓存性能提升: {speedup:.1f}x")
        else:
            logger.warning(f"⚠️  图片缓存性能提升不明显: {first_time:.2f}s -> {second_time:.2f}s")
        
        return True
        
    except Exception as e:
        logger.error(f"PDF图片缓存测试异常: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始测试缓存系统")
    
    test_results = []
    
    # 测试1: PDF图片缓存（不调用VLM）
    test_results.append(("PDF图片缓存", test_cache_without_vlm()))
    
    # 测试2: 缓存管理功能
    test_results.append(("缓存管理功能", test_cache_management()))
    
    # 测试3: 完整缓存系统（包含VLM，可选）
    if "--with-vlm" in sys.argv:
        test_results.append(("完整缓存系统", test_pdf_image_cache()))
    else:
        logger.info("跳过VLM测试（使用 --with-vlm 参数启用）")
    
    # 汇总结果
    logger.info("="*50)
    logger.info("测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 缓存系统测试通过！")
        
        # 显示缓存报告
        logger.info("\n" + "="*50)
        manager = CacheManager()
        manager.print_cache_report()
        
        return True
    else:
        logger.warning("⚠️  部分测试失败，请检查缓存系统。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
