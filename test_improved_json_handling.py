#!/usr/bin/env python3
"""
测试改进后的JSON处理逻辑
"""

import json
from src.utils.llm_client import LLMClient

def test_json_syntax_fixes():
    """测试JSON语法修复功能"""

    print("🔧 测试JSON语法修复功能")
    print("=" * 50)

    # 创建LLM客户端实例来测试清理方法
    llm_client = LLMClient()

    # 测试用例：模拟各种JSON语法问题
    test_cases = [
        {
            "name": "错误的键值对格式",
            "input": '```json\n{"basic_info": *"debt_situation": {":"result"}\n```',
            "expected_fix": True
        },
        {
            "name": "多余的回车符",
            "input": '```json\n{\r\r\n  "status": "符合"\r\r\n}\n```',
            "expected_fix": True
        },
        {
            "name": "多余的逗号",
            "input": '```json\n{"status": "符合",}\n```',
            "expected_fix": True
        },
        {
            "name": "正常的JSON",
            "input": '```json\n{"status": "符合", "details": "正常"}\n```',
            "expected_fix": True
        }
    ]

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试: {test_case['name']}")
        print(f"   输入: {test_case['input'][:50]}...")

        try:
            # 使用清理方法
            cleaned = llm_client._clean_json_response(test_case['input'])
            print(f"   清理后: {cleaned[:50]}...")

            # 尝试解析JSON
            parsed = json.loads(cleaned)
            print("   ✅ JSON解析成功")
            print(f"   结果: {json.dumps(parsed, ensure_ascii=False)}")

        except json.JSONDecodeError as e:
            print(f"   ❌ JSON解析失败: {e}")
        except Exception as e:
            print(f"   ❌ 处理异常: {e}")

def test_complex_json_structure():
    """测试复杂JSON结构处理"""

    print("\n\n🏗️ 测试复杂JSON结构处理")
    print("=" * 50)

    # 模拟一个复杂的审核JSON结构
    complex_json_template = '''
    {
        "basic_info_verification": {
            "material_validity": {
                "completeness": {
                    "identity_card": {"status": "符合", "details": "身份证清晰完整"},
                    "education_materials": {"status": "符合", "details": "学历证明完整"}
                }
            }
        },
        "risk_assessment": {
            "credit_risk": {
                "debt_situation": {"result": "符合", "details": "负债情况正常"}
            }
        }
    }
    '''

    try:
        # 验证模板JSON是否有效
        parsed = json.loads(complex_json_template)
        print("✅ 复杂JSON模板结构有效")
        print(f"   包含 {len(parsed)} 个主要部分")

        # 检查关键字段
        if "basic_info_verification" in parsed:
            print("   ✅ 包含基本信息核验部分")
        if "risk_assessment" in parsed:
            print("   ✅ 包含风险评估部分")

    except json.JSONDecodeError as e:
        print(f"❌ 复杂JSON模板无效: {e}")

if __name__ == "__main__":
    test_json_syntax_fixes()
    test_complex_json_structure()

    print("\n" + "=" * 50)
    print("🎯 改进总结:")
    print("   ✅ 恢复了完整的业务JSON结构")
    print("   ✅ 增强了JSON语法错误修复能力")
    print("   ✅ 保持了详细的审核信息输出")
    print("   ✅ 提供了更好的错误处理机制")