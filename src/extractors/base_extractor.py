"""
基础文档提取器
所有专用提取器的基类
"""

import os
import re
import json
import hashlib
import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from src.utils.pdf_converter import PDFConverter
from config import DOCUMENT_CONFIG

logger = logging.getLogger(__name__)


class BaseExtractor(ABC):
    """文档提取器基类"""

    def __init__(self, cache_dir: str = "cache/extractions", max_workers: Optional[int] = None):
        self.converter = PDFConverter()
        self.document_type = self.get_document_type()
        self.cache_dir = cache_dir

        # 从配置获取并发参数
        vlm_config = DOCUMENT_CONFIG.get("vlm_concurrency", {})
        self.max_workers = max_workers or vlm_config.get("page_level_workers", 3)
        self.api_rate_limit = vlm_config.get("api_rate_limit", {})
        self.retry_config = vlm_config.get("retry_config", {})

        self._ensure_cache_dir()
    
    @abstractmethod
    def get_document_type(self) -> str:
        """返回文档类型标识"""
        pass
    
    @abstractmethod
    def get_extraction_prompt(self) -> str:
        """返回专用的VLM提取提示词"""
        pass
    
    @abstractmethod
    def parse_extracted_text(self, text: str) -> Dict[str, Any]:
        """解析提取的文本，返回结构化数据"""
        pass
    
    def extract_from_pdf(self, pdf_path: str, use_cache: bool = True) -> Dict[str, Any]:
        """从PDF文件提取信息（支持缓存）"""
        if not os.path.exists(pdf_path):
            logger.error(f"PDF文件不存在: {pdf_path}")
            return {"error": "文件不存在"}

        # 检查缓存
        if use_cache:
            cached_result = self._get_cached_result(pdf_path)
            if cached_result:
                logger.info(f"使用缓存结果: {os.path.basename(pdf_path)}")
                return cached_result

        try:
            # 1. PDF转图片
            logger.info(f"开始处理{self.document_type}: {os.path.basename(pdf_path)}")
            images = self.converter.convert_pdf_to_images(
                pdf_path=pdf_path,
                return_dict=True,
                return_base64=True,
                target_longest_dim=1024,
                use_cache=use_cache
            )

            if not images:
                logger.error(f"PDF转图片失败: {pdf_path}")
                return {"error": "PDF转图片失败"}

            # 2. 并发使用VLM提取文本
            all_text = self._extract_text_concurrent(images)

            if not all_text.strip():
                logger.error(f"未能提取到有效文本: {pdf_path}")
                return {"error": "文本提取失败"}

            # 3. 解析提取的文本
            parsed_data = self.parse_extracted_text(all_text)
            # 注意：raw_content 已经在 parse_extracted_text 中设置，不需要重复添加 raw_text
            parsed_data["source_file"] = os.path.basename(pdf_path)
            parsed_data["pages_processed"] = len(images)

            # 4. 缓存结果
            if use_cache:
                self._cache_result(pdf_path, parsed_data)

            logger.info(f"{self.document_type}提取完成: {os.path.basename(pdf_path)}")
            return parsed_data

        except Exception as e:
            logger.error(f"{self.document_type}提取失败: {e}")
            return {"error": str(e)}
    
    def _call_vlm(self, prompt: str, image_base64: str) -> str:
        """调用VLM进行文本提取（带重试机制）"""
        max_retries = self.retry_config.get("max_retries", 3)
        retry_delay = self.retry_config.get("retry_delay_seconds", 2.0)
        exponential_backoff = self.retry_config.get("exponential_backoff", True)
        max_retry_delay = self.retry_config.get("max_retry_delay", 30.0)

        for attempt in range(max_retries + 1):
            try:
                result = self._call_vlm_once(prompt, image_base64)

                # 检查是否成功
                if not result.startswith("[识别失败"):
                    return result

                # 如果是最后一次尝试，返回失败结果
                if attempt == max_retries:
                    return result

                # 等待后重试（限制最大延时）
                delay = retry_delay * (2 ** attempt if exponential_backoff else 1)
                delay = min(delay, max_retry_delay)  # 限制最大延时
                logger.warning(f"VLM调用失败，{delay}秒后重试 (第{attempt+1}次)")
                time.sleep(delay)

            except (ConnectionError, TimeoutError, OSError) as e:
                # 网络相关错误，增加重试
                if attempt == max_retries:
                    logger.error(f"VLM调用网络错误最终失败: {e}")
                    return f"[识别失败: 网络错误 - {str(e)}]"

                delay = retry_delay * (2 ** attempt if exponential_backoff else 1)
                delay = min(delay, max_retry_delay)  # 限制最大延时
                logger.warning(f"VLM调用网络异常，{delay}秒后重试 (第{attempt+1}次): {e}")
                time.sleep(delay)

            except Exception as e:
                if attempt == max_retries:
                    logger.error(f"VLM调用最终失败: {e}")
                    return f"[识别失败: {str(e)}]"

                delay = retry_delay * (2 ** attempt if exponential_backoff else 1)
                delay = min(delay, max_retry_delay)  # 限制最大延时
                logger.warning(f"VLM调用异常，{delay}秒后重试 (第{attempt+1}次): {e}")
                time.sleep(delay)

        return "[识别失败: 重试次数耗尽]"

    def _call_vlm_once(self, prompt: str, image_base64: str) -> str:
        """单次VLM调用"""
        import requests
        import time

        # VLM API配置（硅基智能）
        api_url = "https://api.siliconflow.cn/v1/chat/completions"
        api_key = "sk-plpykhaoetmlvsvzoffnljxsgykhlpwwleewhugruiugglxc"

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": "Qwen/Qwen2.5-VL-72B-Instruct",
            "stream": False,
            "max_tokens": 2048,
            "temperature": 0.1,
            "top_p": 0.7,
            "frequency_penalty": 0.5,
            "n": 1,
            "stop": [],
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}", "detail": "auto"}}
                    ]
                }
            ]
        }

        response = requests.post(api_url, headers=headers, json=payload, timeout=600)

        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                return f"[识别失败: API返回格式错误]"
        else:
            return f"[识别失败: HTTP {response.status_code}]"
    
    def extract_key_value_pairs(self, text: str, patterns: Dict[str, str]) -> Dict[str, str]:
        """使用正则表达式提取键值对"""
        import re
        result = {}
        
        for key, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
            if match:
                result[key] = match.group(1).strip()
            else:
                result[key] = ""
        
        return result
    
    def extract_table_data(self, text: str, table_indicators: List[str]) -> List[Dict[str, str]]:
        """提取表格数据"""
        tables = []
        lines = text.split('\n')
        
        for i, line in enumerate(lines):
            for indicator in table_indicators:
                if indicator in line:
                    # 简单的表格提取逻辑
                    table_start = max(0, i - 2)
                    table_end = min(len(lines), i + 10)
                    table_lines = lines[table_start:table_end]
                    
                    table_data = {
                        "indicator": indicator,
                        "content": '\n'.join(table_lines),
                        "line_number": i + 1
                    }
                    tables.append(table_data)
                    break
        
        return tables
    
    def clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff\-\.\(\)（）]', '', text)
        return text.strip()
    
    def validate_extracted_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证提取的数据"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 基础验证逻辑
        if "error" in data:
            validation_result["is_valid"] = False
            validation_result["errors"].append(data["error"])
        
        if not data.get("raw_content", "").strip():
            validation_result["is_valid"] = False
            validation_result["errors"].append("未提取到有效文本")
        
        return validation_result

    def _ensure_cache_dir(self):
        """确保缓存目录存在"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir, exist_ok=True)

    def _get_cache_key(self, pdf_path: str) -> str:
        """生成缓存键"""
        # 使用文件路径、修改时间和文档类型生成唯一键
        file_stat = os.stat(pdf_path)
        content = f"{pdf_path}_{file_stat.st_mtime}_{file_stat.st_size}_{self.document_type}"
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cached_result(self, pdf_path: str) -> Optional[Dict[str, Any]]:
        """获取缓存结果"""
        try:
            cache_key = self._get_cache_key(pdf_path)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")

            if os.path.exists(cache_file):
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cached_data = json.load(f)
                    logger.debug(f"缓存命中: {os.path.basename(pdf_path)}")
                    return cached_data
        except Exception as e:
            logger.warning(f"读取缓存失败: {e}")

        return None

    def _cache_result(self, pdf_path: str, result: Dict[str, Any]):
        """缓存结果"""
        try:
            cache_key = self._get_cache_key(pdf_path)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")

            # 确保缓存目录存在
            os.makedirs(os.path.dirname(cache_file), exist_ok=True)

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
                logger.debug(f"结果已缓存: {os.path.basename(pdf_path)}")
        except Exception as e:
            logger.warning(f"缓存保存失败: {e}")

    def _extract_text_concurrent(self, images: Dict[str, str]) -> str:
        """并发提取文本"""
        extraction_prompt = self.get_extraction_prompt()
        all_text = ""

        # 如果页数较少，直接串行处理
        if len(images) <= 2:
            for page_num, image_base64 in images.items():
                try:
                    page_text = self._call_vlm(extraction_prompt, image_base64)
                    if page_text and not page_text.startswith("[识别失败"):
                        all_text += f"\n\n=== 第{page_num}页 ===\n{page_text}"
                except Exception as e:
                    logger.warning(f"第{page_num}页提取失败: {e}")
                    continue
            return all_text

        # 多页文档使用并发处理
        page_results = {}

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_page = {
                executor.submit(self._call_vlm, extraction_prompt, image_base64): page_num
                for page_num, image_base64 in images.items()
            }

            # 收集结果
            for future in as_completed(future_to_page):
                page_num = future_to_page[future]
                try:
                    page_text = future.result(timeout=60)  # 60秒超时
                    if page_text and not page_text.startswith("[识别失败"):
                        page_results[page_num] = page_text
                        logger.debug(f"第{page_num}页提取完成")
                    else:
                        logger.warning(f"第{page_num}页提取失败: {page_text}")
                except Exception as e:
                    logger.warning(f"第{page_num}页提取异常: {e}")

        # 按页码顺序组装文本
        sorted_pages = sorted(page_results.keys())
        for page_num in sorted_pages:
            all_text += f"\n\n=== 第{page_num}页 ===\n{page_results[page_num]}"

        return all_text

    def clear_cache(self, older_than_days: int = 7):
        """清理缓存"""
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (older_than_days * 24 * 60 * 60)

            if not os.path.exists(self.cache_dir):
                return

            removed_count = 0
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.json'):
                    file_path = os.path.join(self.cache_dir, filename)
                    if os.path.getmtime(file_path) < cutoff_time:
                        os.remove(file_path)
                        removed_count += 1

            logger.info(f"清理了 {removed_count} 个过期缓存文件")
        except Exception as e:
            logger.warning(f"缓存清理失败: {e}")
