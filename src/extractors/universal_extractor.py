"""
通用文档提取器
使用VLM进行完整的文档内容提取，不做任何业务假设
"""

import logging
from typing import Dict, Any
from .base_extractor import BaseExtractor

logger = logging.getLogger(__name__)


class UniversalExtractor(BaseExtractor):
    """通用文档提取器 - 完整提取所有文档内容"""
    
    def __init__(self, document_type: str = "通用文档", **kwargs):
        """
        初始化通用提取器
        
        Args:
            document_type: 文档类型标识（用于日志）
        """
        self._document_type = document_type
        super().__init__(**kwargs)
    
    def get_document_type(self) -> str:
        return self._document_type
    
    def get_extraction_prompt(self) -> str:
        """返回通用的VLM提取提示词"""
        return (
            "请完整、准确地提取图片中的所有文字内容。\n\n"
            "要求：\n"
            "1. 保持原有的格式和结构\n"
            "2. 不要遗漏任何文字、数字、符号、标点\n"
            "3. 按照从上到下、从左到右的顺序提取\n"
            "4. 如果是表格，请尽量保持表格的行列结构\n"
            "5. 如果有多个区域或栏目，请保持其分隔\n"
            "6. 保留所有可见的文字信息，包括标题、正文、注释、页脚等\n\n"
            "请提供完整的文字内容，不要进行任何筛选、总结或解释。"
        )
    
    def parse_extracted_text(self, text: str) -> Dict[str, Any]:
        """
        不进行任何解析，直接返回原始文本
        
        Args:
            text: VLM提取的原始文本
            
        Returns:
            包含原始文本的字典
        """
        return {
            "extraction_method": "universal_vlm",
            "raw_content": text.strip(),
            "content_length": len(text),
            "line_count": len(text.split('\n')),
            "note": "VLM完整提取的原始文档内容，未经任何结构化处理"
        }
    
    def set_document_type(self, doc_type: str):
        """动态设置文档类型"""
        self._document_type = doc_type
