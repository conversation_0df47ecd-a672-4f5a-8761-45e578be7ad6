import base64
import hashlib
import io
import json
import os
import subprocess
import sys
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

try:
    from PIL import Image
except ImportError:
    print("警告: 未安装PIL/Pillow。安装方法: pip install Pillow")


class PDFConverter:
    """PDF页面转换工具类，提供PDF到图片的多种转换功能。
    
    功能特点:
    - 将PDF页面转换为PNG/JPG/WebP格式
    - 支持保存到文件或返回base64编码
    - 智能计算分辨率保持图像质量
    - 支持批量处理多页PDF
    - 支持旋转图像修正
    """
    
    def __init__(self, check_dependencies: bool = True, cache_dir: str = "cache/images"):
        """初始化转换器

        Args:
            check_dependencies: 是否检查外部依赖 (pdftoppm, pdfinfo)
            cache_dir: 图片缓存目录
        """
        self.temp_files = []
        self.cache_dir = cache_dir
        self._ensure_cache_dir()
        if check_dependencies:
            self._check_dependencies()
    
    def __del__(self):
        """清理临时文件"""
        for tmp_file in self.temp_files:
            try:
                if os.path.exists(tmp_file):
                    os.remove(tmp_file)
            except:
                pass

    def _ensure_cache_dir(self):
        """确保缓存目录存在"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir, exist_ok=True)

    def _get_pdf_cache_key(self, pdf_path: str) -> str:
        """生成PDF文件的缓存键"""
        # 使用文件路径、修改时间和大小生成唯一键
        file_stat = os.stat(pdf_path)
        content = f"{pdf_path}_{file_stat.st_mtime}_{file_stat.st_size}"
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cached_images(self, pdf_path: str) -> Optional[Dict[int, str]]:
        """获取缓存的图片（返回base64格式）"""
        try:
            cache_key = self._get_pdf_cache_key(pdf_path)
            cache_subdir = os.path.join(self.cache_dir, cache_key)

            if not os.path.exists(cache_subdir):
                return None

            # 检查缓存元数据
            metadata_file = os.path.join(cache_subdir, "metadata.json")
            if not os.path.exists(metadata_file):
                return None

            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)

            # 验证页数是否匹配
            expected_pages = self.get_page_count(pdf_path)
            if metadata.get("page_count") != expected_pages:
                return None

            # 加载所有缓存的图片
            cached_images = {}
            for page_num in range(1, expected_pages + 1):
                image_file = os.path.join(cache_subdir, f"page_{page_num:03d}.png")
                if os.path.exists(image_file):
                    with open(image_file, 'rb') as f:
                        image_data = f.read()
                        cached_images[page_num] = base64.b64encode(image_data).decode('utf-8')
                else:
                    return None  # 缺少页面，缓存无效

            return cached_images

        except Exception as e:
            # 缓存读取失败，返回None
            return None

    def _cache_images(self, pdf_path: str, images: Dict[int, str]):
        """缓存图片（输入为base64格式）"""
        try:
            cache_key = self._get_pdf_cache_key(pdf_path)
            cache_subdir = os.path.join(self.cache_dir, cache_key)

            # 创建缓存子目录
            os.makedirs(cache_subdir, exist_ok=True)

            # 保存图片文件
            for page_num, image_base64 in images.items():
                image_file = os.path.join(cache_subdir, f"page_{page_num:03d}.png")
                image_data = base64.b64decode(image_base64)
                with open(image_file, 'wb') as f:
                    f.write(image_data)

            # 保存元数据
            metadata = {
                "pdf_path": pdf_path,
                "page_count": len(images),
                "cached_at": time.time(),
                "cache_key": cache_key
            }

            metadata_file = os.path.join(cache_subdir, "metadata.json")
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

        except Exception as e:
            # 缓存保存失败，不影响主流程
            pass

    @staticmethod
    def _check_dependencies():
        """检查必要的外部依赖是否可用"""
        try:
            subprocess.run(["pdftoppm", "-v"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            subprocess.run(["pdfinfo", "-v"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        except FileNotFoundError:
            error_msg = (
                "未找到必要的依赖。请安装poppler-utils:\n"
                "  Ubuntu/Debian: sudo apt-get install poppler-utils\n"
                "  MacOS: brew install poppler\n"
                "  Windows: 下载并安装poppler: https://github.com/oschwartz10612/poppler-windows/releases/\n\n"
                "安装后确保pdftoppm和pdfinfo在系统路径中"
            )
            print(error_msg, file=sys.stderr)
            raise RuntimeError(error_msg)
    
    def get_page_count(self, pdf_path: str) -> int:
        """获取PDF的总页数
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            页数
        """
        result = subprocess.run(
            ["pdfinfo", pdf_path], 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE, 
            text=True,
            encoding='utf-8',  # 指定编码
            errors='replace'  # 替换无法解码的字符
        )
        
        if result.returncode != 0:
            raise ValueError(f"获取PDF信息失败: {result.stderr}")
        
        for line in result.stdout.splitlines():
            if line.startswith("Pages:"):
                return int(line.split(":")[1].strip())
        
        raise ValueError("无法获取PDF页数")
    
    def get_page_dimensions(self, pdf_path: str, page_num: int) -> Tuple[float, float]:
        """获取特定PDF页面的尺寸
        
        Args:
            pdf_path: PDF文件路径
            page_num: 页码 (从1开始)
            
        Returns:
            (宽度, 高度) 元组，单位为点
        """
        command = [
            "pdfinfo", 
            "-f", str(page_num), 
            "-l", str(page_num), 
            "-box", 
            "-enc", "UTF-8", 
            pdf_path
        ]
        
        result = subprocess.run(
            command, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE, 
            text=True,
            encoding='utf-8',  # 指定编码
            errors='replace'  # 替换无法解码的字符
        )
        
        if result.returncode != 0:
            raise ValueError(f"获取页面尺寸失败: {result.stderr}")
        
        for line in result.stdout.splitlines():
            if "MediaBox" in line:
                media_box_str = line.split(":")[1].strip().split()
                media_box = [float(x) for x in media_box_str]
                return abs(media_box[0] - media_box[2]), abs(media_box[3] - media_box[1])
        
        raise ValueError("未找到MediaBox信息")

    def convert_page_to_image(
        self,
        pdf_path: str,
        page_num: int,
        target_longest_dim: int = 1024,
        dpi: Optional[int] = None,
        format: str = "png",
        output_path: Optional[str] = None,
        return_base64: bool = False,
        return_pil: bool = False,
        high_quality: bool = False,
        rotation: int = 0
    ) -> Union[str, bytes, Image.Image, None]:
        """将PDF的单页转换为图像
        
        Args:
            pdf_path: PDF文件路径
            page_num: 页码 (从1开始)
            target_longest_dim: 输出图像最长边的目标像素值（默认1024）
            dpi: 直接指定DPI值，优先级高于target_longest_dim
            format: 输出格式 ('png', 'jpg', 'webp')
            output_path: 输出文件路径，如果不指定则不保存文件
            return_base64: 是否返回base64编码的字符串
            return_pil: 是否返回PIL Image对象
            high_quality: 使用高质量设置(300 DPI)，适合印刷或OCR
            rotation: 旋转角度(0, 90, 180, 270)
            
        Returns:
            根据参数返回:
            - 如果return_base64=True: 返回base64编码的字符串
            - 如果return_pil=True: 返回PIL Image对象
            - 如果仅指定output_path: 返回None
            - 默认: 返回原始图像bytes
        """
        format = format.lower()
        if format not in ('png', 'jpg', 'jpeg', 'webp'):
            raise ValueError("仅支持png、jpg或webp格式")
        
        if rotation not in (0, 90, 180, 270):
            raise ValueError("旋转角度必须是0, 90, 180或270")
        
        # 计算合适的DPI以获得目标尺寸
        if dpi is not None:
            # 使用指定的DPI
            dpi_value = dpi
        elif high_quality:
            # 使用高质量预设
            dpi_value = 300
        else:
            try:
                width, height = self.get_page_dimensions(pdf_path, page_num)
                longest_dim = max(width, height)
                dpi_value = target_longest_dim * 72 / longest_dim  # 72 DPI是PDF的基准
            except Exception as e:
                # 如果获取尺寸失败，回退到默认值
                dpi_value = 150
                print(f"警告: 无法获取页面尺寸，使用默认DPI {dpi_value}. 错误: {e}")
        
        # 使用pdftoppm将PDF页面转换为PNG
        pdftoppm_args = [
            "pdftoppm",
            "-f", str(page_num),
            "-l", str(page_num),
            "-r", str(dpi_value)
        ]
        
        if format == 'png' or format not in ('jpg', 'jpeg'):
            pdftoppm_args.append("-png")
        else:
            pdftoppm_args.append("-jpeg")
        
        pdftoppm_args.append(pdf_path)
        
        try:
            result = subprocess.run(
                pdftoppm_args,
                timeout=120,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            if result.returncode != 0:
                error_msg = result.stderr.decode('utf-8', errors='replace')
                raise RuntimeError(f"PDF转换失败: {error_msg}")
            
            image_bytes = result.stdout
            
            # 处理旋转
            if rotation != 0 or format == 'webp':
                pil_image = Image.open(io.BytesIO(image_bytes))
                
                # 旋转图像
                if rotation != 0:
                    pil_image = pil_image.rotate(-rotation, expand=True)
                
                # 保存为适当格式
                img_buffer = io.BytesIO()
                if format == 'webp':
                    pil_image.save(img_buffer, format='WEBP', quality=90)
                elif format in ('jpg', 'jpeg'):
                    pil_image = pil_image.convert('RGB')  # 移除透明度
                    pil_image.save(img_buffer, format='JPEG', quality=95)
                else:
                    pil_image.save(img_buffer, format='PNG')
                
                image_bytes = img_buffer.getvalue()
                
                # 保存引用以便后续返回
                if return_pil:
                    pil_image_result = pil_image
            
            # 保存到文件
            if output_path:
                os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
                with open(output_path, 'wb') as f:
                    f.write(image_bytes)
            
            # 返回结果
            if return_pil:
                if 'pil_image_result' in locals():
                    return pil_image_result
                return Image.open(io.BytesIO(image_bytes))
            
            if return_base64:
                return base64.b64encode(image_bytes).decode('utf-8')
            
            if output_path and not (return_base64 or return_pil):
                return None
                
            return image_bytes
            
        except subprocess.TimeoutExpired:
            raise TimeoutError(f"转换页面 {page_num} 超时")
    
    def convert_pdf_to_images(
        self,
        pdf_path: str,
        output_dir: Optional[str] = None,
        output_format: str = "png",
        filename_pattern: str = "page_{:03d}.{ext}",
        target_longest_dim: int = 1024,
        dpi: Optional[int] = None,
        high_quality: bool = False,
        page_range: Optional[Tuple[int, int]] = None,
        return_dict: bool = False,
        return_base64: bool = False,
        use_cache: bool = True
    ) -> Optional[Dict[int, Union[bytes, str]]]:
        """将整个PDF文件转换为图像
        
        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录，如果不指定则不保存文件
            output_format: 输出格式 ('png', 'jpg', 'webp')
            filename_pattern: 文件名模式，使用{:03d}表示页码，{ext}表示扩展名
            target_longest_dim: 输出图像最长边的目标像素值
            dpi: 直接指定DPI值，优先级高于target_longest_dim
            high_quality: 使用高质量设置(300 DPI)，适合印刷或OCR
            page_range: 指定处理的页码范围，如 (1, 5) 表示1-5页，None表示处理所有页
            return_dict: 是否返回包含所有页面图像数据的字典
            return_base64: 当return_dict=True时，是否返回base64编码而非原始bytes
            use_cache: 是否使用图片缓存

        Returns:
            如果return_dict=True: 返回一个字典 {页码: 图像数据}
            否则: 返回None
        """
        if not output_dir and not return_dict:
            raise ValueError("必须指定output_dir或设置return_dict=True")

        # 检查缓存（仅在return_dict=True且return_base64=True时使用）
        if use_cache and return_dict and return_base64 and not page_range:
            cached_images = self._get_cached_images(pdf_path)
            if cached_images:
                return cached_images
            
        page_count = self.get_page_count(pdf_path)
        
        if page_range:
            start_page, end_page = page_range
            start_page = max(1, start_page)
            end_page = min(page_count, end_page)
        else:
            start_page, end_page = 1, page_count
        
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        results = {}
        for page_num in range(start_page, end_page + 1):
            if output_dir:
                ext = output_format.lower().replace('jpeg', 'jpg')
                filename = filename_pattern.format(page_num, ext=ext)
                output_path = os.path.join(output_dir, filename)
            else:
                output_path = None
            
            image_data = self.convert_page_to_image(
                pdf_path=pdf_path,
                page_num=page_num,
                target_longest_dim=target_longest_dim,
                dpi=dpi,
                high_quality=high_quality,
                format=output_format,
                output_path=output_path,
                return_base64=return_base64 and return_dict
            )
            
            if return_dict:
                results[page_num] = image_data
        
        if return_dict:
            # 缓存结果（仅在return_base64=True且未指定page_range时）
            if use_cache and return_base64 and not page_range and results:
                self._cache_images(pdf_path, results)
            return results
        return None
    
    def convert_image_to_pdf(self, image_path: str, output_path: str) -> str:
        """将图像转换为PDF
        
        Args:
            image_path: 图像文件路径
            output_path: 输出PDF路径
            
        Returns:
            输出PDF的路径
        """
        try:
            import img2pdf
        except ImportError:
            raise ImportError("请安装img2pdf: pip install img2pdf")
            
        with open(output_path, "wb") as f:
            f.write(img2pdf.convert(image_path))
        
        return output_path
    
    def get_pdf_text(self, pdf_path: str, page_num: int, engine: str = "pdftotext") -> str:
        """提取PDF页面文本
        
        Args:
            pdf_path: PDF文件路径
            page_num: 页码 (从1开始)
            engine: 文本提取引擎 ('pdftotext' 或 'pdfreport')
            
        Returns:
            提取的文本
        """
        if engine == "pdftotext":
            # 使用pdftotext提取纯文本
            command = [
                "pdftotext",
                "-f", str(page_num),
                "-l", str(page_num),
                "-layout",
                pdf_path,
                "-"
            ]
            
            result = subprocess.run(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace' 
            )
            
            if result.returncode != 0:
                raise ValueError(f"提取文本失败: {result.stderr}")
                
            return result.stdout.strip()
            
        elif engine == "pdfreport":
            # 提取结构化文本，包含位置信息
            # 使用临时文件保存结果
            with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as tmp:
                tmp_path = tmp.name
                self.temp_files.append(tmp_path)
            
            command = [
                "pdftocairo",
                "-f", str(page_num),
                "-l", str(page_num),
                "-txt",
                pdf_path,
                os.path.splitext(tmp_path)[0]
            ]
            
            result = subprocess.run(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            if result.returncode != 0:
                raise ValueError(f"提取文本失败: {result.stderr.decode('utf-8')}")
            
            # 读取生成的文本文件
            with open(f"{os.path.splitext(tmp_path)[0]}.txt", "r", encoding="utf-8", errors="replace") as f:
                return f.read().strip()
        else:
            raise ValueError("不支持的引擎，请使用 'pdftotext' 或 'pdfreport'")


# 使用示例
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="PDF页面转图像工具")
    parser.add_argument("pdf_path", help="输入PDF文件路径")
    parser.add_argument("--output", "-o", help="输出目录或文件路径")
    parser.add_argument("--format", "-f", choices=["png", "jpg", "webp"], default="png", help="输出图像格式")
    parser.add_argument("--resolution", "-r", type=int, choices=[1024, 2048, 4096], default=1024, 
                       help="输出图像分辨率(最长边像素数)")
    parser.add_argument("--high-quality", action="store_true", help="使用高质量模式(300 DPI)")
    parser.add_argument("--page", "-p", type=int, help="仅处理指定页码(从1开始)")
    parser.add_argument("--base64", "-b", action="store_true", help="输出base64编码(仅处理单页时)")
    parser.add_argument("--text", "-t", action="store_true", help="同时提取文本")
    
    args = parser.parse_args()
    
    converter = PDFConverter()
    
    try:
        # 获取页数
        page_count = converter.get_page_count(args.pdf_path)
        print(f"PDF总页数: {page_count}")
        
        if args.page:
            # 处理单页
            if args.page < 1 or args.page > page_count:
                print(f"错误: 页码 {args.page} 超出范围 (1-{page_count})")
                sys.exit(1)
                
            output_path = args.output if args.output else f"page_{args.page}.{args.format}"
            
            if args.base64:
                result = converter.convert_page_to_image(
                    pdf_path=args.pdf_path,
                    page_num=args.page,
                    target_longest_dim=args.resolution,
                    high_quality=args.high_quality,
                    format=args.format,
                    return_base64=True
                )
                print(result)
            else:
                converter.convert_page_to_image(
                    pdf_path=args.pdf_path,
                    page_num=args.page,
                    target_longest_dim=args.resolution,
                    high_quality=args.high_quality,
                    format=args.format,
                    output_path=output_path
                )
                print(f"图像已保存到: {output_path}")
                
            if args.text:
                text = converter.get_pdf_text(args.pdf_path, args.page)
                print("\n提取的文本:")
                print("-" * 40)
                print(text)
                print("-" * 40)
        else:
            # 处理全部页面
            output_dir = args.output if args.output else "output_images"
            
            converter.convert_pdf_to_images(
                pdf_path=args.pdf_path,
                output_dir=output_dir,
                output_format=args.format,
                target_longest_dim=args.resolution,
                high_quality=args.high_quality
            )
            
            print(f"所有图像已保存到目录: {output_dir}")
            
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)