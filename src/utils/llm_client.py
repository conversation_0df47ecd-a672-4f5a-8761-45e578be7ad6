"""
LLM客户端
统一的LLM API调用接口，支持多种LLM提供商
"""

import os
import sys
import json
import logging
import re
from typing import Dict, Any, Optional, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from config import get_llm_config

logger = logging.getLogger(__name__)


class LLMClient:
    """统一的LLM客户端"""
    
    def __init__(self):
        self.config = get_llm_config()
        self.provider = self.config["provider"]
        self._client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化LLM客户端"""
        try:
            if self.provider == "deepseek":
                self._initialize_deepseek()
            elif self.provider == "qwen":
                self._initialize_qwen()
            else:
                raise ValueError(f"不支持的LLM提供商: {self.provider}")

            logger.info(f"LLM客户端初始化成功: {self.provider}")

        except Exception as e:
            logger.error(f"LLM客户端初始化失败: {e}")
            raise
    
    def _initialize_deepseek(self):
        """初始化DeepSeek客户端"""
        try:
            import openai

            self._client = openai.OpenAI(
                api_key=self.config["api_key"],
                base_url=self.config["base_url"]
            )

        except ImportError:
            raise ImportError("请安装openai库: pip install openai")

    def _initialize_qwen(self):
        """初始化Qwen客户端（通过硅基智能）"""
        try:
            import openai

            self._client = openai.OpenAI(
                api_key=self.config["api_key"],
                base_url=self.config["base_url"]
            )

        except ImportError:
            raise ImportError("请安装openai库: pip install openai")
    
    def chat(self, user_message: str, system_message: Optional[str] = None, 
             temperature: Optional[float] = None, max_tokens: Optional[int] = None) -> str:
        """发送聊天消息"""
        try:
            messages = []
            
            if system_message:
                messages.append({"role": "system", "content": system_message})
            
            messages.append({"role": "user", "content": user_message})
            
            # 使用配置中的参数，允许临时覆盖
            temp = temperature if temperature is not None else self.config.get("temperature", 0.1)
            max_tok = max_tokens if max_tokens is not None else self.config.get("max_tokens", 2000)
            
            response = self._client.chat.completions.create(
                model=self.config["model"],
                messages=messages,
                temperature=temp,
                max_tokens=max_tok
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"LLM聊天调用失败: {e}")
            raise
    
    def chat_with_json(self, user_message: str, system_message: Optional[str] = None,
                      temperature: Optional[float] = None, max_tokens: Optional[int] = None) -> Dict[str, Any]:
        """发送聊天消息并期望JSON响应（优先使用Qwen JSON Mode，失败时回退到传统方法）"""
        try:
            messages = []

            # 确保系统消息包含"json"关键词（Qwen JSON Mode要求）
            if system_message:
                json_system = system_message + "\n\n请以有效的JSON格式返回响应。"
            else:
                json_system = "请以有效的JSON格式返回响应。"

            messages.append({"role": "system", "content": json_system})
            messages.append({"role": "user", "content": user_message})

            # 使用配置中的参数，允许临时覆盖
            temp = temperature if temperature is not None else self.config.get("temperature", 0.1)
            max_tok = max_tokens if max_tokens is not None else self.config.get("max_tokens", 2000)

            response_text = None

            # 尝试使用Qwen JSON Mode
            try:
                response = self._client.chat.completions.create(
                    model=self.config["model"],
                    messages=messages,
                    temperature=temp,
                    max_tokens=max_tok,
                    response_format={"type": "json_object"}  # 启用JSON Mode
                )
                response_text = response.choices[0].message.content
                logger.debug("使用JSON Mode成功")

            except Exception as json_mode_error:
                logger.warning(f"JSON Mode失败，回退到传统方法: {json_mode_error}")
                # 回退到传统方法
                response = self._client.chat.completions.create(
                    model=self.config["model"],
                    messages=messages,
                    temperature=temp,
                    max_tokens=max_tok
                )
                response_text = response.choices[0].message.content

            # 解析JSON响应
            try:
                # 智能清理JSON响应
                cleaned_text = self._clean_json_response(response_text)
                return json.loads(cleaned_text)

            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败，返回原始文本: {e}")
                return {
                    "error": "JSON解析失败",
                    "raw_response": response_text
                }

        except Exception as e:
            logger.error(f"LLM JSON聊天调用失败: {e}")
            raise

    def _clean_json_response(self, response_text: str) -> str:
        """智能清理JSON响应，处理各种markdown包装格式和JSON语法问题"""
        cleaned_text = response_text.strip()

        # 1. 首先尝试提取代码块内容
        code_block_pattern = r'```[^`\n]*\n(.*?)```'
        match = re.search(code_block_pattern, cleaned_text, re.DOTALL)

        if match:
            cleaned_text = match.group(1).strip()
        else:
            # 如果没有找到代码块，尝试查找JSON对象
            json_pattern = r'\{.*\}'
            json_match = re.search(json_pattern, cleaned_text, re.DOTALL)
            if json_match:
                cleaned_text = json_match.group(0).strip()

        # 2. 清理常见的JSON语法问题
        cleaned_text = self._fix_json_syntax_issues(cleaned_text)

        return cleaned_text

    def _fix_json_syntax_issues(self, json_text: str) -> str:
        """修复常见的JSON语法问题"""
        # 移除多余的换行符和回车符
        json_text = re.sub(r'\r+', '', json_text)

        # 修复错误的键值对格式，如 *"key": {":"value"
        json_text = re.sub(r'\*"([^"]+)"\s*:\s*\{\s*":\s*"([^"]*)"', r'"\1": "\2"', json_text)

        # 修复不完整的键，如 *"complian*"
        json_text = re.sub(r'\*"[^"]*\*"\s*', '', json_text)

        # 修复中文数字格式问题，如 十九、"担保情况"
        json_text = re.sub(r'[一二三四五六七八九十]+、"([^"]+)"', r'"\1"', json_text)

        # 修复省略号导致的JSON中断，如 "details": "..."
        json_text = re.sub(r'"details":\s*"\.\.\."\s*}', r'"details": "信息不完整"}', json_text)

        # 移除大量重复的空格或制表符
        json_text = re.sub(r'[ \t]{50,}', ' ', json_text)

        # 修复多余的逗号（在}或]前的逗号）
        json_text = re.sub(r',(\s*[}\]])', r'\1', json_text)

        # 修复缺失的逗号（在}或]后应该有逗号的地方）
        json_text = re.sub(r'([}\]])(\s*)(["\{])', r'\1,\2\3', json_text)

        # 修复不完整的键值对，如 "key":>{\"status\": \"
        json_text = re.sub(r'"([^"]+)"\s*:\s*>\s*\{[^}]*\}\s*,?', r'"\1": {"status": "不符合", "details": "信息不完整"},', json_text)

        # 修复包含大量空格的不完整值
        json_text = re.sub(r':\s*"\s{20,}[^"]*"\s*}', r': "信息不完整"}', json_text)

        # 修复不完整的对象结构
        json_text = re.sub(r'"([^"]+)"\s*:\s*\{\s*"[^"]*"\s*:\s*"[^"]*\s*\}\s*,?\s*(?=\})', r'"\1": {"status": "不符合", "details": "信息不完整"}', json_text)

        return json_text
    
    def batch_chat(self, messages: List[Dict[str, str]], 
                  system_message: Optional[str] = None) -> List[str]:
        """批量发送聊天消息"""
        results = []
        
        for i, msg in enumerate(messages):
            try:
                logger.info(f"处理批量消息 {i+1}/{len(messages)}")
                
                user_msg = msg.get("user_message", "")
                temp_system = msg.get("system_message", system_message)
                
                result = self.chat(
                    user_message=user_msg,
                    system_message=temp_system
                )
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"批量消息处理失败 {i+1}: {e}")
                results.append(f"错误: {e}")
        
        return results
    
    def test_connection(self) -> bool:
        """测试LLM连接"""
        try:
            response = self.chat(
                user_message="请回复'连接成功'",
                system_message="你是一个测试助手。",
                temperature=0.0,
                max_tokens=10
            )
            
            success = "连接成功" in response or "成功" in response
            
            if success:
                logger.info("LLM连接测试成功")
            else:
                logger.warning(f"LLM连接测试异常响应: {response}")
            
            return success
            
        except Exception as e:
            logger.error(f"LLM连接测试失败: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "provider": self.provider,
            "model": self.config["model"],
            "base_url": self.config.get("base_url", "N/A"),
            "temperature": self.config.get("temperature", 0.1),
            "max_tokens": self.config.get("max_tokens", 2000)
        }
    
    def estimate_tokens(self, text: str) -> int:
        """估算文本的token数量（粗略估算）"""
        # 简单的token估算：中文字符约1.5个token，英文单词约1个token
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        english_words = len(text.split()) - chinese_chars
        
        estimated_tokens = int(chinese_chars * 1.5 + english_words)
        return estimated_tokens
    
    def check_token_limit(self, text: str) -> bool:
        """检查文本是否超过token限制"""
        estimated = self.estimate_tokens(text)
        limit = self.config.get("max_tokens", 2000)
        
        if estimated > limit * 0.8:  # 留20%余量
            logger.warning(f"文本可能超过token限制: {estimated} > {limit * 0.8}")
            return False
        
        return True


# 全局LLM客户端实例
_global_llm_client = None


def get_global_llm_client() -> LLMClient:
    """获取全局LLM客户端实例"""
    global _global_llm_client
    
    if _global_llm_client is None:
        _global_llm_client = LLMClient()
    
    return _global_llm_client


def test_llm_setup():
    """测试LLM设置"""
    try:
        client = LLMClient()
        
        print("LLM配置信息:")
        info = client.get_model_info()
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        print("\n测试连接...")
        if client.test_connection():
            print("✅ LLM连接成功")
            return True
        else:
            print("❌ LLM连接失败")
            return False
            
    except Exception as e:
        print(f"❌ LLM设置测试失败: {e}")
        return False


if __name__ == "__main__":
    test_llm_setup()
