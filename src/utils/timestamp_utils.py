"""
时间戳工具函数
提供统一的时间戳处理功能
"""

import re
from datetime import datetime
from typing import Optional


def generate_timestamp() -> str:
    """生成标准时间戳格式：YYYYMMDD_HHMMSS"""
    return datetime.now().strftime('%Y%m%d_%H%M%S')


def extract_timestamp_from_audit_id(audit_id: str) -> str:
    """从audit_id中提取时间戳"""
    if audit_id.startswith("audit_"):
        return audit_id.replace("audit_", "")
    return generate_timestamp()


def extract_timestamp_from_filename(filename: str) -> Optional[str]:
    """从文件名中提取时间戳"""
    # 匹配格式：*_audit_result_YYYYMMDD_HHMMSS.json
    match = re.search(r'_audit_result_(\d{8}_\d{6})\.json$', filename)
    if match:
        return match.group(1)
    
    # 匹配格式：*_audit_report_YYYYMMDD_HHMMSS.txt
    match = re.search(r'_audit_report_(\d{8}_\d{6})\.txt$', filename)
    if match:
        return match.group(1)
    
    # 匹配格式：*_YYYYMMDD_HHMMSS.*
    match = re.search(r'_(\d{8}_\d{6})\.[^.]+$', filename)
    if match:
        return match.group(1)
    
    return None


def format_timestamp_for_display(timestamp: str) -> str:
    """将时间戳格式化为可读格式"""
    try:
        # 解析YYYYMMDD_HHMMSS格式
        dt = datetime.strptime(timestamp, '%Y%m%d_%H%M%S')
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except ValueError:
        return timestamp


def generate_audit_result_filename(applicant_name: str, timestamp: str = None) -> str:
    """生成审核结果文件名"""
    if not timestamp:
        timestamp = generate_timestamp()
    return f"{applicant_name}_audit_result_{timestamp}.json"


def generate_audit_report_filename(applicant_name: str, timestamp: str = None) -> str:
    """生成审核报告文件名"""
    if not timestamp:
        timestamp = generate_timestamp()
    return f"{applicant_name}_audit_report_{timestamp}.txt"


def generate_word_report_filename(applicant_name: str, timestamp: str = None) -> str:
    """生成Word报告文件名"""
    if not timestamp:
        timestamp = generate_timestamp()
    return f"{applicant_name}_audit_report_{timestamp}.docx"


def parse_applicant_name_from_filename(filename: str) -> str:
    """从文件名中解析申请人名称"""
    # 移除扩展名
    base_name = filename.rsplit('.', 1)[0]
    
    # 移除时间戳部分
    timestamp = extract_timestamp_from_filename(filename)
    if timestamp:
        # 移除 _audit_result_timestamp 或 _audit_report_timestamp
        if '_audit_result_' in base_name:
            return base_name.replace(f'_audit_result_{timestamp}', '')
        elif '_audit_report_' in base_name:
            return base_name.replace(f'_audit_report_{timestamp}', '')
        else:
            return base_name.replace(f'_{timestamp}', '')
    
    return base_name


def validate_timestamp_format(timestamp: str) -> bool:
    """验证时间戳格式是否正确"""
    try:
        datetime.strptime(timestamp, '%Y%m%d_%H%M%S')
        return True
    except ValueError:
        return False


def get_file_pairs_by_timestamp(directory: str, applicant_name: str = None) -> dict:
    """根据时间戳获取相关文件对"""
    import os
    from pathlib import Path
    
    file_pairs = {}
    dir_path = Path(directory)
    
    if not dir_path.exists():
        return file_pairs
    
    for file_path in dir_path.glob("*.json"):
        filename = file_path.name
        timestamp = extract_timestamp_from_filename(filename)
        
        if not timestamp:
            continue
            
        parsed_applicant = parse_applicant_name_from_filename(filename)
        
        # 如果指定了申请人名称，只处理匹配的文件
        if applicant_name and parsed_applicant != applicant_name:
            continue
        
        if timestamp not in file_pairs:
            file_pairs[timestamp] = {
                'applicant_name': parsed_applicant,
                'audit_result': None,
                'audit_report': None,
                'word_report': None
            }
        
        if '_audit_result_' in filename:
            file_pairs[timestamp]['audit_result'] = str(file_path)
            
        # 检查对应的报告文件
        report_txt = dir_path / generate_audit_report_filename(parsed_applicant, timestamp)
        if report_txt.exists():
            file_pairs[timestamp]['audit_report'] = str(report_txt)
            
        report_docx = dir_path / generate_word_report_filename(parsed_applicant, timestamp)
        if report_docx.exists():
            file_pairs[timestamp]['word_report'] = str(report_docx)
    
    return file_pairs


def cleanup_old_files(directory: str, keep_latest: int = 5) -> list:
    """清理旧文件，保留最新的几个"""
    import os
    from pathlib import Path
    
    dir_path = Path(directory)
    if not dir_path.exists():
        return []
    
    # 按申请人分组文件
    applicant_files = {}
    
    for file_path in dir_path.glob("*_audit_result_*.json"):
        applicant_name = parse_applicant_name_from_filename(file_path.name)
        timestamp = extract_timestamp_from_filename(file_path.name)
        
        if not timestamp:
            continue
            
        if applicant_name not in applicant_files:
            applicant_files[applicant_name] = []
            
        applicant_files[applicant_name].append((timestamp, file_path))
    
    deleted_files = []
    
    # 对每个申请人的文件进行清理
    for applicant_name, files in applicant_files.items():
        # 按时间戳排序，最新的在前
        files.sort(key=lambda x: x[0], reverse=True)
        
        # 删除超出保留数量的文件
        for timestamp, file_path in files[keep_latest:]:
            try:
                # 删除相关的所有文件
                related_files = [
                    file_path,  # audit_result.json
                    dir_path / generate_audit_report_filename(applicant_name, timestamp),  # .txt
                    dir_path / generate_word_report_filename(applicant_name, timestamp)   # .docx
                ]
                
                for related_file in related_files:
                    if related_file.exists():
                        related_file.unlink()
                        deleted_files.append(str(related_file))
                        
            except Exception as e:
                print(f"删除文件失败 {file_path}: {e}")
    
    return deleted_files


def main():
    """测试函数"""
    print("时间戳工具测试")
    print("=" * 50)
    
    # 测试时间戳生成
    timestamp = generate_timestamp()
    print(f"生成时间戳: {timestamp}")
    print(f"格式化显示: {format_timestamp_for_display(timestamp)}")
    
    # 测试文件名生成
    applicant_name = "20160212111(河北石家庄）"
    print(f"\n申请人: {applicant_name}")
    print(f"审核结果文件名: {generate_audit_result_filename(applicant_name, timestamp)}")
    print(f"审核报告文件名: {generate_audit_report_filename(applicant_name, timestamp)}")
    print(f"Word报告文件名: {generate_word_report_filename(applicant_name, timestamp)}")
    
    # 测试文件名解析
    test_filename = generate_audit_result_filename(applicant_name, timestamp)
    print(f"\n测试文件名: {test_filename}")
    print(f"提取时间戳: {extract_timestamp_from_filename(test_filename)}")
    print(f"提取申请人: {parse_applicant_name_from_filename(test_filename)}")
    
    # 测试audit_id提取
    audit_id = f"audit_{timestamp}"
    print(f"\n测试audit_id: {audit_id}")
    print(f"提取时间戳: {extract_timestamp_from_audit_id(audit_id)}")


if __name__ == "__main__":
    main()
