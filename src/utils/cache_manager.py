#!/usr/bin/env python3
"""
缓存管理工具
提供缓存清理、统计和验证功能
"""

import os
import json
import time
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_root: str = "cache"):
        """初始化缓存管理器
        
        Args:
            cache_root: 缓存根目录
        """
        self.cache_root = cache_root
        self.images_cache_dir = os.path.join(cache_root, "images")
        self.extractions_cache_dir = os.path.join(cache_root, "extractions")
    
    def get_cache_stats(self) -> Dict[str, any]:
        """获取缓存统计信息"""
        stats = {
            "images_cache": self._get_directory_stats(self.images_cache_dir),
            "extractions_cache": self._get_directory_stats(self.extractions_cache_dir),
            "total_size_mb": 0,
            "total_files": 0
        }
        
        # 计算总计
        for cache_type in ["images_cache", "extractions_cache"]:
            if stats[cache_type]:
                stats["total_size_mb"] += stats[cache_type]["size_mb"]
                stats["total_files"] += stats[cache_type]["file_count"]
        
        return stats
    
    def _get_directory_stats(self, directory: str) -> Optional[Dict[str, any]]:
        """获取目录统计信息"""
        if not os.path.exists(directory):
            return None
        
        total_size = 0
        file_count = 0
        subdirs = []
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    file_size = os.path.getsize(file_path)
                    total_size += file_size
                    file_count += 1
                except OSError:
                    continue
            
            # 记录子目录
            if root != directory:
                rel_path = os.path.relpath(root, directory)
                if '/' not in rel_path:  # 只记录一级子目录
                    subdirs.append(rel_path)
        
        return {
            "directory": directory,
            "size_mb": round(total_size / (1024 * 1024), 2),
            "file_count": file_count,
            "subdirectories": len(set(subdirs))
        }
    
    def clean_old_cache(self, older_than_days: int = 7) -> Dict[str, int]:
        """清理过期缓存
        
        Args:
            older_than_days: 清理多少天前的缓存
            
        Returns:
            清理统计信息
        """
        cutoff_time = time.time() - (older_than_days * 24 * 60 * 60)
        
        results = {
            "images_removed": 0,
            "extractions_removed": 0,
            "total_size_freed_mb": 0
        }
        
        # 清理图片缓存
        if os.path.exists(self.images_cache_dir):
            results["images_removed"], size_freed = self._clean_directory_by_time(
                self.images_cache_dir, cutoff_time, is_image_cache=True
            )
            results["total_size_freed_mb"] += size_freed
        
        # 清理提取结果缓存
        if os.path.exists(self.extractions_cache_dir):
            results["extractions_removed"], size_freed = self._clean_directory_by_time(
                self.extractions_cache_dir, cutoff_time, is_image_cache=False
            )
            results["total_size_freed_mb"] += size_freed
        
        results["total_size_freed_mb"] = round(results["total_size_freed_mb"], 2)
        return results
    
    def _clean_directory_by_time(self, directory: str, cutoff_time: float, is_image_cache: bool = False) -> Tuple[int, float]:
        """按时间清理目录"""
        removed_count = 0
        size_freed = 0
        
        if is_image_cache:
            # 图片缓存：删除整个子目录
            for subdir in os.listdir(directory):
                subdir_path = os.path.join(directory, subdir)
                if os.path.isdir(subdir_path):
                    metadata_file = os.path.join(subdir_path, "metadata.json")
                    if os.path.exists(metadata_file):
                        try:
                            if os.path.getmtime(metadata_file) < cutoff_time:
                                # 计算目录大小
                                dir_size = self._get_directory_size(subdir_path)
                                shutil.rmtree(subdir_path)
                                removed_count += 1
                                size_freed += dir_size / (1024 * 1024)  # 转换为MB
                        except Exception as e:
                            logger.warning(f"清理图片缓存目录失败 {subdir_path}: {e}")
        else:
            # 提取结果缓存：删除单个JSON文件
            for filename in os.listdir(directory):
                if filename.endswith('.json'):
                    file_path = os.path.join(directory, filename)
                    try:
                        if os.path.getmtime(file_path) < cutoff_time:
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            removed_count += 1
                            size_freed += file_size / (1024 * 1024)  # 转换为MB
                    except Exception as e:
                        logger.warning(f"清理提取缓存文件失败 {file_path}: {e}")
        
        return removed_count, size_freed
    
    def _get_directory_size(self, directory: str) -> int:
        """获取目录总大小（字节）"""
        total_size = 0
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    total_size += os.path.getsize(file_path)
                except OSError:
                    continue
        return total_size
    
    def validate_cache_integrity(self) -> Dict[str, List[str]]:
        """验证缓存完整性"""
        issues = {
            "corrupted_image_cache": [],
            "corrupted_extraction_cache": [],
            "orphaned_files": []
        }
        
        # 验证图片缓存
        if os.path.exists(self.images_cache_dir):
            issues["corrupted_image_cache"] = self._validate_image_cache()
        
        # 验证提取结果缓存
        if os.path.exists(self.extractions_cache_dir):
            issues["corrupted_extraction_cache"] = self._validate_extraction_cache()
        
        return issues
    
    def _validate_image_cache(self) -> List[str]:
        """验证图片缓存完整性"""
        corrupted = []
        
        for subdir in os.listdir(self.images_cache_dir):
            subdir_path = os.path.join(self.images_cache_dir, subdir)
            if os.path.isdir(subdir_path):
                metadata_file = os.path.join(subdir_path, "metadata.json")
                
                if not os.path.exists(metadata_file):
                    corrupted.append(f"缺少元数据文件: {subdir}")
                    continue
                
                try:
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                    
                    expected_pages = metadata.get("page_count", 0)
                    actual_images = len([f for f in os.listdir(subdir_path) 
                                       if f.startswith("page_") and f.endswith(".png")])
                    
                    if actual_images != expected_pages:
                        corrupted.append(f"图片数量不匹配: {subdir} (期望{expected_pages}, 实际{actual_images})")
                        
                except Exception as e:
                    corrupted.append(f"元数据损坏: {subdir} - {e}")
        
        return corrupted
    
    def _validate_extraction_cache(self) -> List[str]:
        """验证提取结果缓存完整性"""
        corrupted = []
        
        for filename in os.listdir(self.extractions_cache_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(self.extractions_cache_dir, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 检查必要字段
                    required_fields = ["raw_content", "source_file", "pages_processed"]
                    missing_fields = [field for field in required_fields if field not in data]
                    
                    if missing_fields:
                        corrupted.append(f"缺少字段: {filename} - {missing_fields}")
                        
                except Exception as e:
                    corrupted.append(f"JSON损坏: {filename} - {e}")
        
        return corrupted
    
    def clear_all_cache(self) -> bool:
        """清空所有缓存"""
        try:
            if os.path.exists(self.cache_root):
                shutil.rmtree(self.cache_root)
                logger.info("所有缓存已清空")
                return True
        except Exception as e:
            logger.error(f"清空缓存失败: {e}")
            return False

        return True

    def clear_image_cache(self) -> bool:
        """只清空图片缓存"""
        try:
            if os.path.exists(self.images_cache_dir):
                shutil.rmtree(self.images_cache_dir)
                os.makedirs(self.images_cache_dir, exist_ok=True)
                logger.info("图片缓存已清空")
                return True
        except Exception as e:
            logger.error(f"清空图片缓存失败: {e}")
            return False

        return True

    def clear_extraction_cache(self) -> bool:
        """只清空提取结果缓存"""
        try:
            if os.path.exists(self.extractions_cache_dir):
                shutil.rmtree(self.extractions_cache_dir)
                os.makedirs(self.extractions_cache_dir, exist_ok=True)
                logger.info("提取结果缓存已清空")
                return True
        except Exception as e:
            logger.error(f"清空提取结果缓存失败: {e}")
            return False

        return True

    def auto_clear_cache_if_needed(self, clear_before_run: bool = False, clear_images: bool = False, clear_extractions: bool = False):
        """根据配置自动清理缓存"""
        if clear_before_run:
            logger.info("配置要求运行前清空所有缓存")
            return self.clear_all_cache()
        else:
            success = True
            if clear_images:
                logger.info("配置要求清空图片缓存")
                success &= self.clear_image_cache()

            if clear_extractions:
                logger.info("配置要求清空提取结果缓存")
                success &= self.clear_extraction_cache()

            return success
    
    def print_cache_report(self):
        """打印缓存报告"""
        print("=" * 50)
        print("缓存状态报告")
        print("=" * 50)
        
        # 统计信息
        stats = self.get_cache_stats()
        print(f"总缓存大小: {stats['total_size_mb']} MB")
        print(f"总文件数: {stats['total_files']}")
        print()
        
        # 图片缓存
        if stats["images_cache"]:
            img_stats = stats["images_cache"]
            print(f"图片缓存: {img_stats['size_mb']} MB, {img_stats['file_count']} 文件, {img_stats['subdirectories']} 个PDF缓存")
        else:
            print("图片缓存: 无")
        
        # 提取结果缓存
        if stats["extractions_cache"]:
            ext_stats = stats["extractions_cache"]
            print(f"提取结果缓存: {ext_stats['size_mb']} MB, {ext_stats['file_count']} 文件")
        else:
            print("提取结果缓存: 无")
        
        print()
        
        # 完整性检查
        issues = self.validate_cache_integrity()
        total_issues = sum(len(issue_list) for issue_list in issues.values())
        
        if total_issues == 0:
            print("✅ 缓存完整性检查通过")
        else:
            print(f"⚠️  发现 {total_issues} 个缓存问题:")
            for issue_type, issue_list in issues.items():
                if issue_list:
                    print(f"  {issue_type}: {len(issue_list)} 个问题")
        
        print("=" * 50)


if __name__ == "__main__":
    # 命令行工具
    import argparse
    
    parser = argparse.ArgumentParser(description="缓存管理工具")
    parser.add_argument("--stats", action="store_true", help="显示缓存统计")
    parser.add_argument("--clean", type=int, metavar="DAYS", help="清理N天前的缓存")
    parser.add_argument("--validate", action="store_true", help="验证缓存完整性")
    parser.add_argument("--clear-all", action="store_true", help="清空所有缓存")
    parser.add_argument("--cache-dir", default="cache", help="缓存目录路径")
    
    args = parser.parse_args()
    
    manager = CacheManager(args.cache_dir)
    
    if args.stats:
        manager.print_cache_report()
    elif args.clean:
        results = manager.clean_old_cache(args.clean)
        print(f"清理完成: 删除 {results['images_removed']} 个图片缓存, {results['extractions_removed']} 个提取缓存")
        print(f"释放空间: {results['total_size_freed_mb']} MB")
    elif args.validate:
        issues = manager.validate_cache_integrity()
        total_issues = sum(len(issue_list) for issue_list in issues.values())
        if total_issues == 0:
            print("✅ 缓存完整性检查通过")
        else:
            print(f"⚠️  发现 {total_issues} 个问题:")
            for issue_type, issue_list in issues.items():
                for issue in issue_list:
                    print(f"  - {issue}")
    elif args.clear_all:
        if manager.clear_all_cache():
            print("✅ 所有缓存已清空")
        else:
            print("❌ 清空缓存失败")
    else:
        manager.print_cache_report()
