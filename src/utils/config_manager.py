#!/usr/bin/env python3
"""
配置管理工具
支持配置文件生成、验证和管理
"""

import os
import json
import yaml
import argparse
import logging
from typing import Dict, Any, Optional
from pathlib import Path

# 添加项目根目录到Python路径
import sys
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from config import (
    LLM_CONFIG, VLM_CONFIG, DOCUMENT_CONFIG, AUDIT_CONFIG, 
    TEST_CONFIG, SYSTEM_CONFIG, LOGGING_CONFIG,
    override_config_from_args, validate_config
)

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_templates = {
            "basic": self._get_basic_template(),
            "advanced": self._get_advanced_template(),
            "development": self._get_development_template(),
            "production": self._get_production_template()
        }
    
    def generate_config_file(self, template_name: str = "basic", output_file: str = "user_config.yaml") -> bool:
        """生成配置文件"""
        try:
            if template_name not in self.config_templates:
                logger.error(f"未知的配置模板: {template_name}")
                return False
            
            config = self.config_templates[template_name]
            
            # 根据文件扩展名选择格式
            if output_file.endswith('.json'):
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
            elif output_file.endswith('.yaml') or output_file.endswith('.yml'):
                with open(output_file, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            else:
                logger.error("不支持的配置文件格式，请使用 .json 或 .yaml")
                return False
            
            logger.info(f"配置文件已生成: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"生成配置文件失败: {e}")
            return False
    
    def load_config_file(self, config_file: str) -> Optional[Dict[str, Any]]:
        """加载配置文件"""
        try:
            if not os.path.exists(config_file):
                logger.error(f"配置文件不存在: {config_file}")
                return None
            
            if config_file.endswith('.json'):
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            elif config_file.endswith('.yaml') or config_file.endswith('.yml'):
                with open(config_file, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
            else:
                logger.error("不支持的配置文件格式")
                return None
                
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return None
    
    def apply_config_file(self, config_file: str) -> bool:
        """应用配置文件"""
        config = self.load_config_file(config_file)
        if not config:
            return False
        
        try:
            # 转换为命令行参数格式
            args_dict = self._config_to_args(config)
            override_config_from_args(args_dict)
            logger.info(f"配置文件已应用: {config_file}")
            return True
            
        except Exception as e:
            logger.error(f"应用配置文件失败: {e}")
            return False
    
    def validate_current_config(self) -> bool:
        """验证当前配置"""
        try:
            validate_config()
            logger.info("✅ 配置验证通过")
            return True
        except Exception as e:
            logger.error(f"❌ 配置验证失败: {e}")
            return False
    
    def show_current_config(self):
        """显示当前配置"""
        config = {
            "llm_config": LLM_CONFIG,
            "vlm_config": VLM_CONFIG,
            "document_config": DOCUMENT_CONFIG,
            "audit_config": AUDIT_CONFIG,
            "test_config": TEST_CONFIG,
            "system_config": SYSTEM_CONFIG,
            "logging_config": LOGGING_CONFIG
        }
        
        print("=== 当前系统配置 ===")
        print(yaml.dump(config, default_flow_style=False, allow_unicode=True, indent=2))
    
    def _get_basic_template(self) -> Dict[str, Any]:
        """基础配置模板"""
        return {
            "# 基础配置模板": "适用于一般用户",
            "data_dir": "data0724",
            "cache_dir": "cache",
            "output_dir": "results",
            "page_workers": 2,
            "doc_workers": 3,
            "user_workers": 2,
            "enable_cache": True,
            "api_rate_limit": 30,
            "max_retries": 3
        }
    
    def _get_advanced_template(self) -> Dict[str, Any]:
        """高级配置模板"""
        return {
            "# 高级配置模板": "适用于高级用户",
            "data_dir": "data0724",
            "cache_dir": "cache",
            "output_dir": "results",
            "page_workers": 4,
            "doc_workers": 6,
            "user_workers": 3,
            "enable_cache": True,
            "api_rate_limit": 60,
            "max_retries": 5,
            "batch_delay_seconds": 1.0,
            "concurrent_limit": 8,
            "cache_ttl_hours": 48,
            "max_cache_size_mb": 2000
        }
    
    def _get_development_template(self) -> Dict[str, Any]:
        """开发配置模板"""
        return {
            "# 开发配置模板": "适用于开发和测试",
            "data_dir": "test_data",
            "cache_dir": "dev_cache",
            "output_dir": "dev_results",
            "page_workers": 1,
            "doc_workers": 2,
            "user_workers": 1,
            "enable_cache": True,
            "api_rate_limit": 10,
            "max_retries": 2,
            "enable_vlm_tests": True,
            "enable_llm_tests": False,
            "test_timeout_seconds": 120,
            "max_test_files": 1
        }
    
    def _get_production_template(self) -> Dict[str, Any]:
        """生产配置模板"""
        return {
            "# 生产配置模板": "适用于生产环境",
            "data_dir": "production_data",
            "cache_dir": "production_cache",
            "output_dir": "production_results",
            "page_workers": 3,
            "doc_workers": 5,
            "user_workers": 3,
            "enable_cache": True,
            "api_rate_limit": 50,
            "max_retries": 5,
            "batch_delay_seconds": 2.0,
            "concurrent_limit": 10,
            "cache_ttl_hours": 72,
            "max_cache_size_mb": 5000,
            "cleanup_temp_files": True,
            "max_concurrent_audits": 5
        }
    
    def _config_to_args(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """将配置文件转换为命令行参数格式"""
        args_dict = {}
        
        # 直接映射的参数
        direct_mappings = [
            "data_dir", "cache_dir", "output_dir",
            "page_workers", "doc_workers", "user_workers",
            "api_rate_limit", "max_retries"
        ]

        for key in direct_mappings:
            if key in config:
                args_dict[key] = config[key]

        # 特殊处理
        if "enable_cache" in config and not config["enable_cache"]:
            args_dict["disable_cache"] = True

        # 缓存控制配置
        cache_control = config.get("cache_control", {})
        if cache_control.get("clear_before_run"):
            args_dict["clear_cache_before_run"] = True
        if cache_control.get("clear_images_before_run"):
            args_dict["clear_images_before_run"] = True
        if cache_control.get("clear_extractions_before_run"):
            args_dict["clear_extractions_before_run"] = True
        
        return args_dict


def main():
    """命令行工具主函数"""
    parser = argparse.ArgumentParser(description="配置管理工具")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 生成配置文件
    gen_parser = subparsers.add_parser("generate", help="生成配置文件")
    gen_parser.add_argument("--template", choices=["basic", "advanced", "development", "production"], 
                           default="basic", help="配置模板")
    gen_parser.add_argument("--output", default="user_config.yaml", help="输出文件名")
    
    # 验证配置
    subparsers.add_parser("validate", help="验证当前配置")
    
    # 显示配置
    subparsers.add_parser("show", help="显示当前配置")
    
    # 应用配置文件
    apply_parser = subparsers.add_parser("apply", help="应用配置文件")
    apply_parser.add_argument("config_file", help="配置文件路径")
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    manager = ConfigManager()
    
    if args.command == "generate":
        success = manager.generate_config_file(args.template, args.output)
        exit(0 if success else 1)
    
    elif args.command == "validate":
        success = manager.validate_current_config()
        exit(0 if success else 1)
    
    elif args.command == "show":
        manager.show_current_config()
        exit(0)
    
    elif args.command == "apply":
        success = manager.apply_config_file(args.config_file)
        exit(0 if success else 1)
    
    else:
        parser.print_help()
        exit(1)


if __name__ == "__main__":
    main()
