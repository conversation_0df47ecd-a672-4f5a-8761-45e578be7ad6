"""
补充材料智能分类器
专门用于识别和分类7号及以后的补充材料
"""

import os
import logging
from typing import Optional, Dict, Any, List
from src.extractors.universal_extractor import UniversalExtractor

logger = logging.getLogger(__name__)


class SupplementaryMaterialClassifier:
    """补充材料智能分类器"""
    
    def __init__(self):
        self.extractor = UniversalExtractor()
        
        # 补充材料类型定义
        self.supplementary_types = {
            "7": {
                "name": "谈话记录",
                "keywords": ["谈话", "记录", "面谈", "interview", "访谈", "对话"],
                "content_indicators": ["谈话时间", "谈话地点", "谈话人", "记录人", "问答", "Q:", "A:"]
            },
            "8": {
                "name": "岗位证书", 
                "keywords": ["证书", "资格", "岗位", "certificate", "认证", "执业"],
                "content_indicators": ["证书编号", "发证机关", "有效期", "持证人", "资格证书", "专业技术"]
            },
            "9": {
                "name": "工作证明",
                "keywords": ["工作证明", "在职", "证明", "employment", "任职", "职务"],
                "content_indicators": ["兹证明", "在我单位", "工作期间", "职务", "月薪", "特此证明"]
            },
            "10": {
                "name": "其他补充材料",
                "keywords": ["补充", "其他", "additional", "附件", "说明"],
                "content_indicators": []
            }
        }
    
    def classify_supplementary_material(self, file_path: str, use_content_analysis: bool = True) -> str:
        """
        分类补充材料
        
        Args:
            file_path: 文件路径
            use_content_analysis: 是否使用内容分析
            
        Returns:
            文档类型编号 (7, 8, 9, 10)
        """
        filename = os.path.basename(file_path).lower()
        
        # 1. 首先尝试文件名关键词匹配
        filename_type = self._classify_by_filename(filename)
        
        # 2. 如果需要内容分析且文件名分类不确定
        if use_content_analysis and (filename_type == "10" or filename_type is None):
            content_type = self._classify_by_content(file_path)
            if content_type and content_type != "10":
                logger.info(f"通过内容分析识别文档类型: {filename} -> {self.supplementary_types[content_type]['name']}")
                return content_type
        
        # 3. 返回文件名分类结果或默认类型
        result_type = filename_type or "10"
        logger.info(f"补充材料分类: {filename} -> {self.supplementary_types[result_type]['name']}")
        return result_type
    
    def _classify_by_filename(self, filename: str) -> Optional[str]:
        """基于文件名进行分类"""
        for doc_type, config in self.supplementary_types.items():
            if doc_type == "10":  # 跳过默认类型
                continue
                
            for keyword in config["keywords"]:
                if keyword in filename:
                    return doc_type
        
        return "10"  # 默认为其他补充材料
    
    def _classify_by_content(self, file_path: str) -> Optional[str]:
        """基于文档内容进行分类"""
        try:
            # 提取文档内容
            logger.info(f"开始内容分析: {os.path.basename(file_path)}")
            extracted_data = self.extractor.extract_from_pdf(file_path, use_cache=True)
            
            if "error" in extracted_data:
                logger.warning(f"内容提取失败: {extracted_data['error']}")
                return None
            
            content = extracted_data.get("raw_content", "").lower()
            if not content.strip():
                logger.warning("提取的内容为空")
                return None
            
            # 分析内容特征
            return self._analyze_content_features(content)
            
        except Exception as e:
            logger.error(f"内容分析失败: {e}")
            return None
    
    def _analyze_content_features(self, content: str) -> Optional[str]:
        """分析内容特征来确定文档类型"""
        content_lower = content.lower()
        
        # 计算每种类型的匹配分数
        type_scores = {}
        
        for doc_type, config in self.supplementary_types.items():
            if doc_type == "10":  # 跳过默认类型
                continue
                
            score = 0
            
            # 关键词匹配得分
            for keyword in config["keywords"]:
                if keyword in content_lower:
                    score += 2
            
            # 内容指示器匹配得分
            for indicator in config["content_indicators"]:
                if indicator.lower() in content_lower:
                    score += 3
            
            if score > 0:
                type_scores[doc_type] = score
        
        # 返回得分最高的类型
        if type_scores:
            best_type = max(type_scores.items(), key=lambda x: x[1])
            if best_type[1] >= 3:  # 最低置信度阈值
                logger.info(f"内容分析结果: {self.supplementary_types[best_type[0]]['name']} (得分: {best_type[1]})")
                return best_type[0]
        
        logger.info("内容分析无法确定具体类型，归类为其他补充材料")
        return "10"
    
    def get_classification_confidence(self, file_path: str) -> Dict[str, Any]:
        """获取分类置信度信息"""
        filename = os.path.basename(file_path).lower()
        
        # 文件名匹配分析
        filename_matches = {}
        for doc_type, config in self.supplementary_types.items():
            if doc_type == "10":
                continue
            matches = [kw for kw in config["keywords"] if kw in filename]
            if matches:
                filename_matches[doc_type] = matches
        
        return {
            "filename": filename,
            "filename_matches": filename_matches,
            "recommended_type": self.classify_supplementary_material(file_path, use_content_analysis=False),
            "content_analysis_available": True
        }
    
    def get_type_name(self, doc_type: str) -> str:
        """获取文档类型名称"""
        return self.supplementary_types.get(doc_type, {}).get("name", "未知类型")
    
    def get_all_supplementary_types(self) -> Dict[str, str]:
        """获取所有补充材料类型"""
        return {doc_type: config["name"] for doc_type, config in self.supplementary_types.items()}
