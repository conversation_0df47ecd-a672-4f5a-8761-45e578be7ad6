"""
增强文档管理器
负责管理和分类用户提交的文档，支持6类标准文档 + 补充材料
"""

import os
import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
from src.utils.supplementary_classifier import SupplementaryMaterialClassifier

logger = logging.getLogger(__name__)


class EnhancedDocumentManager:
    """增强文档管理器 - 处理6类标准文档 + 补充材料"""
    
    def __init__(self, applicant_dir: str):
        self.applicant_dir = applicant_dir

        # 标准文档类型映射
        self.standard_document_types = {
            "1": "申请表",
            "2": "身份证复印件",
            "3": "征信报告",
            "4": "教育背景",
            "5": "邮政履历",
            "6": "执行网公开信息"
        }

        # 初始化补充材料分类器
        self.supplementary_classifier = SupplementaryMaterialClassifier()

        self.documents = {}
        self.scan_documents()
    
    def scan_documents(self):
        """扫描申请人目录下的所有文档 - 使用强制映射策略"""
        if not os.path.exists(self.applicant_dir):
            logger.error(f"申请人目录不存在: {self.applicant_dir}")
            return

        pdf_files = []
        for root, dirs, files in os.walk(self.applicant_dir):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(root, file))

        logger.info(f"发现 {len(pdf_files)} 个PDF文件")

        if not pdf_files:
            logger.warning("未发现任何PDF文件")
            return

        # 按文件名排序，确保分配的一致性
        pdf_files.sort()

        logger.info("开始强制映射文档类型:")

        # 强制映射前6个文件到标准文档类型
        for i, pdf_file in enumerate(pdf_files):
            if i < 6:  # 前6个文件强制映射到1-6类型
                doc_type = str(i + 1)
                self.documents[doc_type] = {
                    "path": pdf_file,
                    "filename": os.path.basename(pdf_file),
                    "type_name": self.standard_document_types[doc_type],
                    "is_standard": True,
                    "assignment_method": "强制映射"
                }
                logger.info(f"强制映射: {os.path.basename(pdf_file)} -> {doc_type}-{self.standard_document_types[doc_type]}")

            else:  # 第7个及以后的文件使用智能分类
                doc_type = self.supplementary_classifier.classify_supplementary_material(pdf_file)
                type_name = self.supplementary_classifier.get_type_name(doc_type)

                # 处理重复类型的情况
                original_doc_type = doc_type
                counter = 1
                while doc_type in self.documents:
                    doc_type = f"{original_doc_type}_{counter}"
                    counter += 1

                self.documents[doc_type] = {
                    "path": pdf_file,
                    "filename": os.path.basename(pdf_file),
                    "type_name": type_name,
                    "is_standard": False,
                    "assignment_method": "智能分类",
                    "original_type": original_doc_type
                }
                logger.info(f"智能分类: {os.path.basename(pdf_file)} -> {doc_type}-{type_name}")

        # 记录分类结果
        standard_count = sum(1 for doc in self.documents.values() if doc["is_standard"])
        supplementary_count = len(self.documents) - standard_count

        logger.info(f"文档分类完成: 标准文档 {standard_count} 个, 补充材料 {supplementary_count} 个")

        # 检查必需文档
        self.check_required_documents()
    
    def classify_document(self, file_path: str) -> Optional[str]:
        """
        文档分类方法（已弃用，现在使用强制映射策略）
        保留此方法以兼容旧代码
        """
        logger.warning("classify_document方法已弃用，现在使用强制映射策略")
        return None
    
    def get_type_name(self, doc_type: str) -> str:
        """获取文档类型名称"""
        # 处理带后缀的类型（如 "7_1", "8_2"）
        base_type = doc_type.split('_')[0]

        if base_type in self.standard_document_types:
            return self.standard_document_types[base_type]
        else:
            return self.supplementary_classifier.get_type_name(base_type)
    
    def check_required_documents(self):
        """检查必需文档是否齐全"""
        missing_docs = []
        for doc_type, type_name in self.standard_document_types.items():
            if doc_type not in self.documents:
                missing_docs.append(f"{doc_type}-{type_name}")
        
        if missing_docs:
            logger.warning(f"缺少必需文档: {', '.join(missing_docs)}")
        else:
            logger.info("所有必需文档齐全")
    
    def get_document_path(self, doc_type: str) -> Optional[str]:
        """获取指定类型文档的路径"""
        if doc_type in self.documents:
            return self.documents[doc_type]["path"]
        return None
    
    def get_all_documents(self) -> Dict[str, Dict]:
        """获取所有文档信息"""
        return self.documents
    
    def get_standard_documents(self) -> Dict[str, Dict]:
        """获取标准文档"""
        return {k: v for k, v in self.documents.items() if v["is_standard"]}
    
    def get_supplementary_documents(self) -> Dict[str, Dict]:
        """获取补充材料"""
        return {k: v for k, v in self.documents.items() if not v["is_standard"]}
    
    def get_document_list_for_extraction(self) -> List[Tuple[str, str, str]]:
        """获取用于提取的文档列表 (doc_type, path, type_name)"""
        doc_list = []
        for doc_type, doc_info in self.documents.items():
            doc_list.append((doc_type, doc_info["path"], doc_info["type_name"]))
        return doc_list
    
    def has_supplementary_materials(self) -> bool:
        """检查是否有补充材料"""
        return len(self.get_supplementary_documents()) > 0
    
    def get_supplementary_material_types(self) -> List[str]:
        """获取补充材料类型列表"""
        supplementary_docs = self.get_supplementary_documents()
        return [doc_info["type_name"] for doc_info in supplementary_docs.values()]
    
    def get_document_summary(self) -> Dict[str, Any]:
        """获取文档概要信息"""
        standard_docs = self.get_standard_documents()
        supplementary_docs = self.get_supplementary_documents()

        return {
            "total_documents": len(self.documents),
            "standard_documents": {
                "count": len(standard_docs),
                "types": [doc["type_name"] for doc in standard_docs.values()],
                "assignment_method": "强制映射"
            },
            "supplementary_materials": {
                "count": len(supplementary_docs),
                "types": [doc["type_name"] for doc in supplementary_docs.values()],
                "assignment_method": "智能分类"
            },
            "missing_required": self.get_missing_required_documents(),
            "classification_strategy": "前6个文件强制映射到标准类型，第7个及以后智能分类"
        }

    def get_classification_details(self) -> Dict[str, Any]:
        """获取详细的分类信息"""
        details = {
            "classification_strategy": "强制映射 + 智能分类",
            "standard_documents": {},
            "supplementary_materials": {},
            "assignment_summary": {
                "forced_assignments": 0,
                "intelligent_classifications": 0,
                "total_files": len(self.documents)
            }
        }

        for doc_type, doc_info in self.documents.items():
            doc_detail = {
                "filename": doc_info["filename"],
                "type_name": doc_info["type_name"],
                "assignment_method": doc_info.get("assignment_method", "未知"),
                "path": doc_info["path"]
            }

            if doc_info["is_standard"]:
                details["standard_documents"][doc_type] = doc_detail
                details["assignment_summary"]["forced_assignments"] += 1
            else:
                details["supplementary_materials"][doc_type] = doc_detail
                details["assignment_summary"]["intelligent_classifications"] += 1

                # 添加智能分类的额外信息
                if "original_type" in doc_info:
                    doc_detail["original_type"] = doc_info["original_type"]

        return details
    
    def get_missing_required_documents(self) -> List[str]:
        """获取缺失的必需文档"""
        missing = []
        for doc_type, type_name in self.standard_document_types.items():
            if doc_type not in self.documents:
                missing.append(type_name)
        return missing
    
    def validate_document_completeness(self) -> Tuple[bool, List[str]]:
        """验证文档完整性"""
        missing = self.get_missing_required_documents()
        is_complete = len(missing) == 0
        return is_complete, missing
