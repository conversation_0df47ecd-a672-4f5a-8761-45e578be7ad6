"""
批量审核处理器
实现多用户并发审核
"""

import os
import time
import logging
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from datetime import datetime

from config import DOCUMENT_CONFIG
from src.core.intelligent_audit_service import IntelligentAuditService

logger = logging.getLogger(__name__)


@dataclass
class AuditTask:
    """审核任务"""
    applicant_dir: str
    applicant_name: str
    task_id: str


class BatchAuditProcessor:
    """批量审核处理器"""
    
    def __init__(self, use_concurrent_extraction: bool = True):
        # 获取并发配置
        vlm_config = DOCUMENT_CONFIG.get("vlm_concurrency", {})
        self.user_workers = vlm_config.get("user_level_workers", 3)
        self.batch_delay = vlm_config.get("api_rate_limit", {}).get("batch_delay_seconds", 1.0)
        
        self.use_concurrent_extraction = use_concurrent_extraction
        
        # 统计信息
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "start_time": None,
            "end_time": None
        }
    
    def process_batch(self, data_dir: str, output_dir: str = "batch_results") -> Dict[str, Any]:
        """批量处理多个申请人"""
        logger.info(f"开始批量审核: {data_dir}")
        
        # 1. 扫描申请人目录
        tasks = self._scan_applicant_directories(data_dir)
        
        if not tasks:
            logger.warning(f"未找到申请人目录: {data_dir}")
            return {"error": "未找到申请人目录"}
        
        # 2. 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 3. 执行批量审核
        self.stats["total_tasks"] = len(tasks)
        self.stats["start_time"] = datetime.now()
        
        results = self._execute_batch_audit(tasks, output_dir)
        
        self.stats["end_time"] = datetime.now()
        
        # 4. 生成批量报告
        batch_report = self._generate_batch_report(results)
        
        logger.info(f"批量审核完成: {self.stats['completed_tasks']}/{self.stats['total_tasks']}")
        return batch_report
    
    def _scan_applicant_directories(self, data_dir: str) -> List[AuditTask]:
        """扫描申请人目录"""
        tasks = []
        
        if not os.path.exists(data_dir):
            logger.error(f"数据目录不存在: {data_dir}")
            return tasks
        
        for item in os.listdir(data_dir):
            item_path = os.path.join(data_dir, item)
            
            # 检查是否为目录且包含PDF文件
            if os.path.isdir(item_path):
                pdf_files = [f for f in os.listdir(item_path) if f.lower().endswith('.pdf')]
                
                if pdf_files:
                    task = AuditTask(
                        applicant_dir=item_path,
                        applicant_name=item,
                        task_id=f"audit_{len(tasks)+1:03d}"
                    )
                    tasks.append(task)
                    logger.debug(f"发现申请人: {item} ({len(pdf_files)} 个PDF文件)")
        
        logger.info(f"扫描完成: 发现 {len(tasks)} 个申请人")
        return tasks
    
    def _execute_batch_audit(self, tasks: List[AuditTask], output_dir: str) -> Dict[str, Any]:
        """执行批量审核"""
        results = {}
        
        # 如果任务数量较少，使用串行处理
        if len(tasks) <= 2:
            logger.info("任务数量较少，使用串行处理")
            for task in tasks:
                result = self._audit_single_applicant(task, output_dir)
                results[task.applicant_name] = result
            return results
        
        # 多任务并发处理
        logger.info(f"使用 {self.user_workers} 个线程并发处理 {len(tasks)} 个申请人")
        
        with ThreadPoolExecutor(max_workers=self.user_workers) as executor:
            # 提交所有任务
            future_to_task = {
                executor.submit(self._audit_single_applicant, task, output_dir): task
                for task in tasks
            }
            
            # 收集结果
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                
                try:
                    result = future.result(timeout=1800)  # 30分钟超时
                    
                    results[task.applicant_name] = result
                    
                    if result.get("success", False):
                        self.stats["completed_tasks"] += 1
                        logger.info(f"✅ 审核完成 ({self.stats['completed_tasks']}/{self.stats['total_tasks']}): {task.applicant_name}")
                    else:
                        self.stats["failed_tasks"] += 1
                        logger.error(f"❌ 审核失败: {task.applicant_name} - {result.get('error', '未知错误')}")
                
                except Exception as e:
                    self.stats["failed_tasks"] += 1
                    logger.error(f"❌ 审核异常: {task.applicant_name} - {e}")
                    results[task.applicant_name] = {
                        "success": False,
                        "error": str(e),
                        "task_id": task.task_id
                    }
                
                # 添加批次间延时
                if self.stats["completed_tasks"] + self.stats["failed_tasks"] < self.stats["total_tasks"]:
                    time.sleep(self.batch_delay)
        
        return results
    
    def _audit_single_applicant(self, task: AuditTask, output_dir: str) -> Dict[str, Any]:
        """审核单个申请人"""
        try:
            # 创建审核服务实例
            audit_service = IntelligentAuditService(
                use_concurrent_extraction=self.use_concurrent_extraction
            )
            
            # 执行审核
            logger.debug(f"开始审核: {task.applicant_name}")
            audit_result = audit_service.audit_applicant(task.applicant_dir)
            
            # 保存结果
            result_file = os.path.join(output_dir, f"{task.applicant_name}_audit_result.json")
            
            import json
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(audit_result, f, ensure_ascii=False, indent=2, default=str)
            
            return {
                "success": True,
                "audit_id": audit_result.get("audit_id"),
                "result_file": result_file,
                "task_id": task.task_id,
                "document_count": audit_result.get("document_summary", {}).get("total_documents", 0)
            }
            
        except Exception as e:
            logger.error(f"审核失败 {task.applicant_name}: {e}")
            return {
                "success": False,
                "error": str(e),
                "task_id": task.task_id
            }
    
    def _generate_batch_report(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成批量审核报告"""
        duration = None
        if self.stats["start_time"] and self.stats["end_time"]:
            duration = (self.stats["end_time"] - self.stats["start_time"]).total_seconds()
        
        successful_audits = [name for name, result in results.items() if result.get("success", False)]
        failed_audits = [name for name, result in results.items() if not result.get("success", False)]
        
        report = {
            "batch_summary": {
                "total_applicants": self.stats["total_tasks"],
                "successful_audits": len(successful_audits),
                "failed_audits": len(failed_audits),
                "success_rate": len(successful_audits) / self.stats["total_tasks"] * 100 if self.stats["total_tasks"] > 0 else 0,
                "duration_seconds": duration,
                "start_time": self.stats["start_time"].isoformat() if self.stats["start_time"] else None,
                "end_time": self.stats["end_time"].isoformat() if self.stats["end_time"] else None
            },
            "successful_applicants": successful_audits,
            "failed_applicants": failed_audits,
            "detailed_results": results,
            "performance_metrics": {
                "average_time_per_applicant": duration / self.stats["total_tasks"] if duration and self.stats["total_tasks"] > 0 else None,
                "concurrent_workers": self.user_workers,
                "concurrent_extraction_enabled": self.use_concurrent_extraction
            }
        }
        
        return report
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self.stats.copy()
