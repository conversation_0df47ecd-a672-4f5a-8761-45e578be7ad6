"""
智能审核服务
基于LLM和完整规则文档的审核服务
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from src.core.document_based_rules_manager import DocumentBasedRulesManager
from src.core.enhanced_document_manager import EnhancedDocumentManager
from src.core.concurrent_document_extractor import ConcurrentDocumentExtractor
from src.extractors.base_extractor import BaseExtractor
from src.extractors.universal_extractor import UniversalExtractor
from src.utils.llm_client import LLMClient

logger = logging.getLogger(__name__)


class IntelligentAuditService:
    """智能审核服务 - 基于LLM和完整规则文档"""
    
    def __init__(self, rules_dir: str = "规则文档", use_concurrent_extraction: bool = True):
        self.rules_manager = DocumentBasedRulesManager(rules_dir)
        self.llm_client = LLMClient()
        self.use_concurrent_extraction = use_concurrent_extraction

        # 初始化并发文档提取器
        if use_concurrent_extraction:
            self.concurrent_extractor = ConcurrentDocumentExtractor()

        # 文档类型名称映射
        self.document_type_names = {
            "1": "申请表",
            "2": "身份证",
            "3": "征信报告",
            "4": "教育背景",
            "5": "邮政履历",
            "6": "执行网信息",
            "supplementary": "补充材料"
        }
    
    def audit_applicant(self, applicant_dir: str) -> Dict[str, Any]:
        """对申请人进行完整审核"""
        try:
            logger.info(f"开始审核申请人: {applicant_dir}")
            
            # 1. 扫描和分类文档
            doc_manager = EnhancedDocumentManager(applicant_dir)
            
            # 2. 提取所有文档内容
            extracted_data = self._extract_all_documents(doc_manager)
            
            # 3. 基于规则文档的动态分析
            rule_based_analysis = self._perform_rule_based_analysis(extracted_data)
            
            # 6. 构建完整结果
            document_summary = doc_manager.get_document_summary()
            classification_details = doc_manager.get_classification_details()

            # 添加提取成功统计
            successful_extractions = len([
                doc for doc in extracted_data.values()
                if doc.get("content") and "error" not in doc.get("content", {})
            ])
            document_summary["successful_extractions"] = successful_extractions
            document_summary["failed_extractions"] = len(extracted_data) - successful_extractions

            # 添加详细的分类信息
            document_summary["classification_details"] = classification_details

            # 分析token使用情况
            token_analysis = self._analyze_token_usage(extracted_data, rule_based_analysis)

            # 构建纯净的审核报告（不包含raw_content）
            audit_result = {
                "audit_id": f"audit_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "applicant_dir": applicant_dir,
                "audit_date": datetime.now().isoformat(),
                "document_summary": document_summary,
                "rule_based_analysis": rule_based_analysis,
                "token_usage_analysis": token_analysis,
                "extraction_cache_info": {
                    "note": "文档提取结果已缓存，可通过cache/extractions/目录查看详细内容",
                    "cache_location": "cache/extractions/",
                    "documents_processed": len(extracted_data),
                    "extraction_files": [
                        {
                            "type": doc_data.get("type_name", "未知类型"),
                            "filename": doc_data.get("filename", "未知文件"),
                            "extraction_method": doc_data.get("content", {}).get("extraction_method", "未知方法"),
                            "content_length": doc_data.get("content", {}).get("content_length", 0),
                            "pages_processed": doc_data.get("content", {}).get("pages_processed", 0)
                        }
                        for doc_data in extracted_data.values()
                        if doc_data.get("content")
                    ]
                }
            }
            
            logger.info("审核完成")
            return audit_result
            
        except Exception as e:
            logger.error(f"审核过程中发生错误: {e}")
            raise
    
    def _extract_all_documents(self, doc_manager: EnhancedDocumentManager) -> Dict[str, Any]:
        """提取所有文档内容（支持并发）"""
        if self.use_concurrent_extraction:
            logger.info("使用并发模式提取文档")
            return self.concurrent_extractor.extract_all_documents(
                doc_manager.applicant_dir,
                use_cache=True
            )
        else:
            logger.info("使用串行模式提取文档")
            return self._extract_documents_sequential(doc_manager)

    def _extract_documents_sequential(self, doc_manager: EnhancedDocumentManager) -> Dict[str, Any]:
        """串行提取文档内容（原有逻辑）"""
        extracted_data = {}

        # 获取所有文档
        all_documents = doc_manager.get_all_documents()

        for doc_type, doc_info in all_documents.items():
            try:
                logger.info(f"提取文档: {doc_info['type_name']}")

                # 获取文档类型名称
                document_type_name = self.document_type_names.get(doc_type, doc_info.get("type_name", f"文档类型{doc_type}"))

                # 创建通用提取器
                extractor = UniversalExtractor(document_type=document_type_name)

                # 提取内容
                content = extractor.extract_from_pdf(doc_info["path"])
                extracted_data[doc_type] = {
                    "type_name": doc_info["type_name"],
                    "filename": doc_info["filename"],
                    "content": content
                }

            except Exception as e:
                logger.error(f"提取文档失败 {doc_info['type_name']}: {e}")
                extracted_data[doc_type] = {
                    "type_name": doc_info["type_name"],
                    "filename": doc_info["filename"],
                    "content": {},
                    "error": str(e)
                }

        return extracted_data

    def _perform_rule_based_analysis(self, extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """基于规则文档进行动态分析"""
        logger.info("开始基于规则文档的动态分析")

        # 获取完整的规则文档内容
        rules_content = self.rules_manager.get_full_rules_content()

        # 构建LLM提示词
        prompt = self._build_rule_based_analysis_prompt(extracted_data, rules_content)

        # 调用LLM进行分析
        try:
            system_message = "你是一个专业的信贷审核专家。请根据提供的规则文档和申请人材料进行全面分析，并以JSON格式返回结构化的审核结果。"

            analysis_result = self.llm_client.chat_with_json(
                user_message=prompt,
                system_message=system_message,
                return_token_stats=True
            )
            logger.info("规则分析完成")
            return analysis_result
        except Exception as e:
            logger.error(f"规则分析失败: {e}")
            return {
                "analysis_status": "失败",
                "error": str(e),
                "verification_results": [],
                "risk_assessment": {},
                "recommendations": []
            }


    

    





    

    

    

    


    def _build_rule_based_analysis_prompt(self, extracted_data: Dict[str, Any], rules_content: str) -> str:
        """构建基于规则文档的分析提示词 - 严格按照审单规则格式"""

        # 收集所有提取的文本内容
        all_extracted_text = ""
        for doc_type, doc_data in extracted_data.items():
            content = doc_data.get("content", {})
            if isinstance(content, dict) and "raw_content" in content:
                type_name = doc_data.get("type_name", f"文档{doc_type}")
                filename = doc_data.get("filename", "未知文件")
                raw_content = content["raw_content"]

                all_extracted_text += f"\n\n=== {type_name} ({filename}) ===\n"
                all_extracted_text += raw_content

        prompt = f"""
你是邮政银行代理金融部的专业审核专家。请严格按照提供的审核规则文档对申请人材料进行全面审核。

## 审核规则文档：
{rules_content}

## 申请人提交的所有文档内容：
{all_extracted_text}

## 审核要求：
1. **严格遵循规则文档**：请仔细阅读上述审核规则文档，严格按照文档中的要求、标准和流程进行审核
2. **客观分析**：基于文档内容进行客观分析，避免主观臆断
3. **完整覆盖**：确保审核覆盖规则文档中要求的所有审核项目和检查点
4. **格式一致**：输出结构应与规则文档中的审核报告格式保持一致
5. **申请表版式检查**：申请表必须包含以下24个字段，不能多也不能少：
   - 1、姓名
   - 2、性别
   - 3、出生日期
   - 4、民族
   - 5、籍贯
   - 6、政治面貌
   - 7、用工性质
   - 8、学历/学位
   - 9、毕业院校
   - 10、专业
   - 11、身份证号码
   - 12、银行业从业是否满两年
   - 13、现任岗位
   - 14、现任机构
   - 15、拟任机构
   - 16、申请单位
   - 17、申请人电话
   - 18、申请单位联系人
   - 19、联系人电话
   - 20、工作简历
   - 21、个人及家庭负债情况
   - 22、主要家庭成员及社会关系（包含关系、姓名、年龄、政治面貌、单位及职务子项）
   - 23、申请人承诺
   - 24、区县邮政分公司意见

   **重要**：请严格按照上述24个字段进行检查。字段匹配规则：
   - "区县邮政分公司意见" 可以匹配 "县(区)邮政分公司意见" 或 "区县邮政分公司意见"
   - "个人及家庭负债情况" 不能匹配 "个人及家庭情况"，必须包含"负债"关键词
   - 如果申请表中包含其他字段（如"市邮政分公司金融业务部意见"、"市邮政分公司人力资源部意见"等），应视为多余字段
   - 只有上述24个字段（或其合理变体）是标准字段，其他级别的审批意见都属于多余字段

   请逐一核对申请表内容，确认是否包含上述所有24个字段，并识别任何多余字段，在format_integrity中详细报告检查结果。

6. **材料清晰度智能评估**：通过分析VLM识别结果的准确性来判断材料清晰度
   **评估原则**：
   - **VLM识别准确性分析**：材料不清晰会导致VLM识别困难，产生幻觉信息
   - **错误识别指标**：
     * 明显的乱码或无意义字符（如：ÿffff、□□□、###、重复的特殊符号等）
     * 重复的无关信息（如：同一段文字重复多次出现）
     * 格式错误（如：身份证号码格式不正确、日期格式异常、电话号码格式错误）
     * 逻辑矛盾（如：同一文档内信息自相矛盾、不合理的数据）
     * 关键信息缺失或识别为乱码
   - **跨文档一致性**：相同信息在不同文档中的一致性，不一致可能表明某些材料不清晰
   - **关键信息完整性**：重要字段是否被正确识别和提取

   **清晰度判断标准**：
   - **符合**：VLM识别结果准确，关键信息清晰完整，无明显识别错误或乱码
   - **不符合**：VLM识别结果包含大量错误、乱码、重复信息或关键信息缺失，表明原始材料可能不清晰

   **具体评估要求**：
   请对每个文档分别进行清晰度评估，重点检查：
   - **申请表**：字段识别准确性、表格结构完整性、手写内容可读性
   - **身份证复印件**：姓名、身份证号码、地址等关键信息是否清晰可读
   - **征信报告**：报告内容是否完整、数据是否准确、是否有重复或乱码
   - **学历材料**：证书信息、学校名称、专业等是否清晰
   - **人力系统截图**：系统界面是否清晰、数据是否完整
   - **执行网信息**：查询结果是否清晰、信息是否完整

   如发现清晰度问题，请在clarity_issues_found中详细列出具体问题。

7. **身份信息一致性核验**：重点核查各类文件身份信息一致性，包括：
   - 申请表 vs 身份证复印件：姓名、身份证号码、出生日期等
   - 申请表 vs 征信报告：姓名、身份证号码等关键身份信息
   - 申请表 vs 学历材料：姓名、身份证号码等
   - 申请表 vs 人力系统截图：姓名、身份证号码、工作履历等
   - 所有文件间的交叉验证：确保所有文件中的身份信息完全一致

**重要提醒**：请务必返回有效的JSON格式，确保所有括号、引号、逗号都正确匹配，不要包含任何注释、特殊字符或格式错误。

请以JSON格式返回审核结果，严格按照以下完整结构：
{{
    "basic_info_verification": {{
        "material_validity": {{
            "completeness": {{
                "identity_card": {{"status": "符合/不符合", "details": "具体说明"}},
                "education_materials": {{"status": "符合/不符合", "details": "具体说明"}},
                "hr_system_screenshot": {{"status": "符合/不符合", "details": "具体说明"}},
                "credit_report": {{"status": "符合/不符合", "details": "具体说明"}},
                "execution_info_screenshot": {{"status": "符合/不符合", "details": "具体说明"}}
            }},
            "clarity": {{
                "overall_status": "符合/不符合",
                "overall_details": "总体清晰度评估",
                "document_clarity_analysis": {{
                    "application_form": {{"status": "符合/不符合", "details": "申请表清晰度分析，包括VLM识别准确性、是否有乱码、重复信息等"}},
                    "identity_card": {{"status": "符合/不符合", "details": "身份证复印件清晰度分析"}},
                    "credit_report": {{"status": "符合/不符合", "details": "征信报告清晰度分析"}},
                    "education_materials": {{"status": "符合/不符合", "details": "学历材料清晰度分析"}},
                    "hr_system_screenshot": {{"status": "符合/不符合", "details": "人力系统截图清晰度分析"}},
                    "execution_info_screenshot": {{"status": "符合/不符合", "details": "执行网信息截图清晰度分析"}}
                }},
                "clarity_issues_found": [
                    {{"document": "文档名称", "issue_type": "问题类型", "description": "具体问题描述"}}
                ]
            }},
            "application_form": {{
                "format_integrity": {{
                    "status": "符合/不符合",
                    "details": "版式完整性检查结果",
                    "field_check": {{
                        "required_fields_count": "实际包含的必需字段数量/24",
                        "missing_fields": ["缺失的字段名称列表"],
                        "extra_fields": ["多余的字段名称列表"],
                        "field_validation_details": "详细的字段验证说明"
                    }}
                }},
                "information_completeness": {{"status": "符合/不符合", "details": "信息完整性"}}
            }},
            "credit_materials": {{
                "date_validity": {{"status": "符合/不符合", "details": "征信报告日期", "credit_date": "提取的征信报告日期"}}
            }}
        }},
        "information_consistency": {{
            "identity_verification": {{
                "status": "符合/不符合",
                "details": "身份信息一致性检查结果",
                "document_comparison": {{
                    "application_form_vs_id_card": {{"status": "符合/不符合", "details": "申请表与身份证复印件对比结果"}},
                    "application_form_vs_credit_report": {{"status": "符合/不符合", "details": "申请表与征信报告对比结果"}},
                    "application_form_vs_education_materials": {{"status": "符合/不符合", "details": "申请表与学历材料对比结果"}},
                    "application_form_vs_hr_screenshot": {{"status": "符合/不符合", "details": "申请表与人力系统截图对比结果"}},
                    "cross_document_consistency": {{"status": "符合/不符合", "details": "所有文件间身份信息交叉验证结果"}}
                }}
            }},
            "key_audit_items": {{
                "education": {{"status": "符合/不符合", "details": "申请表和学历证明的一致性"}},
                "work_experience": {{"status": "符合/不符合", "details": "申请表与人力系统截图的一致性"}},
                "debt_situation": {{"status": "符合/不符合", "details": "申请表与征信报告的一致性"}}
            }}
        }},
        "education_verification": {{"status": "符合/不符合", "details": "大专及以上学历证明"}}
    }},
    "risk_assessment": {{
        "main_risk_points": [
            {{"description": "风险点描述", "details": "具体情况"}}
        ],
        "credit_risk": {{
            "credit_card_situation": {{"result": "符合/不符合", "details": "信用卡办理情况"}},
            "debt_situation": {{"result": "符合/不符合", "details": "月还款额，负债总额"}},
            "non_bank_loans": {{"result": "符合/不符合", "details": "是否有，金额，频次如何"}},
            "business_loans": {{"result": "符合/不符合", "details": "是否有，金额"}},
            "guarantee_situation": {{"result": "符合/不符合", "details": "是否有，金额"}},
            "credit_restrictions": {{"result": "符合/不符合", "details": "符合禁入/瑕疵标准（详细原因）"}},
            "abnormal_info": {{"result": "符合/不符合", "details": "5年内欠税记录、强制执行记录、电信欠费记录"}}
        }},
        "compliance_risk": {{
            "execution_info_cases": [
                {{"case": "案例信息", "details": "具体情况"}}
            ],
            "criminal_record_check": {{"result": "符合/不符合", "details": "违法犯罪记录检查"}}
        }}
    }},
    "system_judgment": {{
        "auto_decision": "通过/不通过",
        "decision_reason": "系统判断理由",
        "confidence_level": "高/中/低"
    }},
    "next_steps_recommendation": [
        "下一步操作建议1",
        "下一步操作建议2"
    ]
}}

**JSON格式要求**：
1. 确保所有字符串值都用双引号包围
2. 确保所有对象键都用双引号包围
3. 不要在JSON中包含注释或额外的文本
4. 确保所有括号、花括号、方括号正确匹配
5. 数组最后一个元素后不要有逗号
6. 对象最后一个属性后不要有逗号
"""

        return prompt

    def _analyze_token_usage(self, extracted_data: Dict[str, Any], rule_based_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """分析token使用情况"""
        try:
            # 分析文档内容的token分布
            document_token_analysis = self.llm_client.analyze_document_tokens(extracted_data)

            # 分析固定提示词的token使用
            system_message = "你是一个专业的信贷审核专家。请根据提供的规则文档和申请人材料进行全面分析，并以JSON格式返回结构化的审核结果。"
            rules_content = self.rules_manager.get_all_rules_content()

            # 构建提示词模板（不包含具体文档内容）
            prompt_template = self._build_rule_based_analysis_prompt({}, rules_content)

            # 计算各部分token使用
            system_tokens = self.llm_client.estimate_tokens(system_message)
            rules_tokens = self.llm_client.estimate_tokens(rules_content)
            prompt_template_tokens = self.llm_client.estimate_tokens(prompt_template) - document_token_analysis["total_document_tokens"]

            # 从LLM响应中提取token统计（如果可用）
            llm_token_stats = rule_based_analysis.get("_token_stats", {})

            token_analysis = {
                "input_breakdown": {
                    "system_message": {
                        "content": "系统提示词",
                        "estimated_tokens": system_tokens
                    },
                    "rules_content": {
                        "content": "审核规则文档",
                        "estimated_tokens": rules_tokens
                    },
                    "prompt_template": {
                        "content": "提示词模板（不含文档内容）",
                        "estimated_tokens": prompt_template_tokens
                    },
                    "documents": document_token_analysis["documents"],
                    "total_document_tokens": document_token_analysis["total_document_tokens"],
                    "total_fixed_tokens": system_tokens + rules_tokens + prompt_template_tokens,
                    "total_input_tokens": system_tokens + rules_tokens + prompt_template_tokens + document_token_analysis["total_document_tokens"]
                },
                "document_statistics": {
                    "document_count": document_token_analysis["document_count"],
                    "average_tokens_per_document": round(document_token_analysis["total_document_tokens"] / max(document_token_analysis["document_count"], 1), 2),
                    "largest_document": max(
                        document_token_analysis["documents"].items(),
                        key=lambda x: x[1]["estimated_tokens"],
                        default=("无", {"estimated_tokens": 0})
                    ),
                    "smallest_document": min(
                        document_token_analysis["documents"].items(),
                        key=lambda x: x[1]["estimated_tokens"],
                        default=("无", {"estimated_tokens": 0})
                    )
                },
                "llm_response_stats": llm_token_stats,
                "cost_estimation": {
                    "note": "基于估算token数量，实际费用可能有差异",
                    "model": self.llm_client.config["model"],
                    "estimated_input_tokens": system_tokens + rules_tokens + prompt_template_tokens + document_token_analysis["total_document_tokens"],
                    "estimated_output_tokens": llm_token_stats.get("output_tokens", 0)
                }
            }

            return token_analysis

        except Exception as e:
            logger.error(f"Token分析失败: {e}")
            return {
                "error": f"Token分析失败: {e}",
                "basic_stats": {
                    "document_count": len(extracted_data),
                    "total_document_tokens": "计算失败"
                }
            }
