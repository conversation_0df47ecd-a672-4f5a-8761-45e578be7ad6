"""
基本信息核验模块
负责身份一致性检查和基本资格验证
"""

import re
import logging
from typing import Dict, Any, List
from src.schemas.audit_result_schema import VerificationResult, IdentityConsistency, BasicVerification

logger = logging.getLogger(__name__)


class BasicVerificationEngine:
    """基本信息核验引擎"""
    
    def __init__(self):
        self.verification_rules = self._load_verification_rules()
    
    def verify_identity_consistency(self, extracted_data: Dict[str, Any]) -> IdentityConsistency:
        """验证身份一致性"""
        logger.info("开始身份一致性验证")
        
        # 提取各文档中的关键信息
        name_consistency = self._verify_name_consistency(extracted_data)
        id_consistency = self._verify_id_card_consistency(extracted_data)
        education_consistency = self._verify_education_consistency(extracted_data)
        career_consistency = self._verify_career_consistency(extracted_data)
        debt_consistency = self._verify_debt_consistency(extracted_data)
        
        return IdentityConsistency(
            name_consistency=name_consistency,
            id_card_consistency=id_consistency,
            education_consistency=education_consistency,
            career_consistency=career_consistency,
            debt_consistency=debt_consistency
        )
    
    def verify_material_validity(self, extracted_data: Dict[str, Any]) -> Dict[str, VerificationResult]:
        """验证材料有效性"""
        logger.info("开始材料有效性验证")
        
        validity_results = {}
        
        # 验证申请表有效性
        if "1" in extracted_data:  # 申请表
            validity_results["application_form"] = self._verify_application_form_validity(
                extracted_data["1"]
            )
        
        # 验证身份证有效性
        if "2" in extracted_data:  # 身份证
            validity_results["id_card"] = self._verify_id_card_validity(
                extracted_data["2"]
            )
        
        # 验证征信报告时效性
        if "3" in extracted_data:  # 征信报告
            validity_results["credit_report"] = self._verify_credit_report_timeliness(
                extracted_data["3"]
            )
        
        # 验证学历证明
        if "4" in extracted_data:  # 教育背景
            validity_results["education_certificate"] = self._verify_education_certificate(
                extracted_data["4"]
            )
        
        return validity_results
    
    def verify_education_requirement(self, extracted_data: Dict[str, Any]) -> VerificationResult:
        """验证学历要求（大专及以上）"""
        logger.info("开始学历要求验证")
        
        if "4" not in extracted_data:
            return VerificationResult(
                result="不符合",
                details="缺少教育背景文件",
                evidence=["未提供学历证明文件"]
            )
        
        education_data = extracted_data["4"]
        qualification_check = education_data.get("qualification_check", {})
        
        if qualification_check.get("meets_education_requirement", False):
            degree_level = education_data.get("education_info", {}).get("degree_level", "")
            return VerificationResult(
                result="符合",
                details=f"学历层次符合要求: {degree_level}",
                evidence=[f"学历层次: {degree_level}"]
            )
        else:
            issues = qualification_check.get("issues", [])
            return VerificationResult(
                result="不符合",
                details="学历要求不符合",
                evidence=issues
            )
    
    def _verify_name_consistency(self, extracted_data: Dict[str, Any]) -> VerificationResult:
        """验证姓名一致性"""
        names = {}
        
        # 从各文档提取姓名
        doc_types = {
            "1": "申请表",
            "2": "身份证", 
            "3": "征信报告",
            "4": "教育背景",
            "5": "邮政履历"
        }
        
        for doc_type, doc_name in doc_types.items():
            if doc_type in extracted_data:
                name = self._extract_name_from_document(extracted_data[doc_type], doc_type)
                if name:
                    names[doc_name] = name
        
        if not names:
            return VerificationResult(
                result="无法判断",
                details="未能从任何文档中提取到姓名信息",
                evidence=[]
            )
        
        # 检查姓名一致性
        unique_names = set(names.values())
        if len(unique_names) == 1:
            return VerificationResult(
                result="符合",
                details="各文档姓名一致",
                evidence=[f"统一姓名: {list(unique_names)[0]}"]
            )
        else:
            evidence = [f"{doc}: {name}" for doc, name in names.items()]
            return VerificationResult(
                result="不符合",
                details="各文档姓名不一致",
                evidence=evidence
            )
    
    def _verify_id_card_consistency(self, extracted_data: Dict[str, Any]) -> VerificationResult:
        """验证身份证号一致性"""
        id_numbers = {}
        
        # 从各文档提取身份证号
        doc_types = {
            "1": "申请表",
            "2": "身份证",
            "3": "征信报告", 
            "4": "教育背景",
            "5": "邮政履历"
        }
        
        for doc_type, doc_name in doc_types.items():
            if doc_type in extracted_data:
                id_number = self._extract_id_number_from_document(extracted_data[doc_type], doc_type)
                if id_number:
                    id_numbers[doc_name] = id_number
        
        if not id_numbers:
            return VerificationResult(
                result="无法判断",
                details="未能从任何文档中提取到身份证号",
                evidence=[]
            )
        
        # 检查身份证号一致性
        unique_ids = set(id_numbers.values())
        if len(unique_ids) == 1:
            return VerificationResult(
                result="符合",
                details="各文档身份证号一致",
                evidence=[f"统一身份证号: {list(unique_ids)[0]}"]
            )
        else:
            evidence = [f"{doc}: {id_num}" for doc, id_num in id_numbers.items()]
            return VerificationResult(
                result="不符合",
                details="各文档身份证号不一致",
                evidence=evidence
            )
    
    def _verify_education_consistency(self, extracted_data: Dict[str, Any]) -> VerificationResult:
        """验证学历信息一致性"""
        # 比较申请表和学历证明中的学历信息
        application_education = ""
        certificate_education = ""
        
        if "1" in extracted_data:  # 申请表
            app_data = extracted_data["1"]
            application_education = app_data.get("education_info", {}).get("highest_degree", "")
        
        if "4" in extracted_data:  # 教育背景
            edu_data = extracted_data["4"]
            certificate_education = edu_data.get("education_info", {}).get("degree_level", "")
        
        if not application_education and not certificate_education:
            return VerificationResult(
                result="无法判断",
                details="缺少学历信息",
                evidence=[]
            )
        
        if application_education and certificate_education:
            if self._normalize_education_level(application_education) == self._normalize_education_level(certificate_education):
                return VerificationResult(
                    result="符合",
                    details="申请表与学历证明中的学历信息一致",
                    evidence=[f"申请表: {application_education}", f"学历证明: {certificate_education}"]
                )
            else:
                return VerificationResult(
                    result="不符合",
                    details="申请表与学历证明中的学历信息不一致",
                    evidence=[f"申请表: {application_education}", f"学历证明: {certificate_education}"]
                )
        
        return VerificationResult(
            result="无法判断",
            details="学历信息不完整",
            evidence=[f"申请表: {application_education}", f"学历证明: {certificate_education}"]
        )
    
    def _verify_career_consistency(self, extracted_data: Dict[str, Any]) -> VerificationResult:
        """验证工作经历一致性"""
        # 比较申请表和邮政履历中的工作信息
        application_work = []
        career_work = []
        
        if "1" in extracted_data:  # 申请表
            app_data = extracted_data["1"]
            application_work = app_data.get("work_experience", [])
        
        if "5" in extracted_data:  # 邮政履历
            career_data = extracted_data["5"]
            career_work = career_data.get("work_history", [])
        
        if not application_work and not career_work:
            return VerificationResult(
                result="无法判断",
                details="缺少工作经历信息",
                evidence=[]
            )
        
        # 简单的一致性检查（可以进一步完善）
        if application_work and career_work:
            return VerificationResult(
                result="符合",
                details="申请表与邮政履历均包含工作经历信息",
                evidence=[f"申请表工作经历: {len(application_work)}条", f"邮政履历: {len(career_work)}条"]
            )
        
        return VerificationResult(
            result="无法判断",
            details="工作经历信息不完整",
            evidence=[]
        )
    
    def _verify_debt_consistency(self, extracted_data: Dict[str, Any]) -> VerificationResult:
        """验证负债信息一致性"""
        # 比较申请表和征信报告中的负债信息
        application_debt = ""
        credit_debt = ""
        
        if "1" in extracted_data:  # 申请表
            app_data = extracted_data["1"]
            financial_info = app_data.get("financial_info", {})
            application_debt = financial_info.get("total_debt", "") or financial_info.get("monthly_payment", "")
        
        if "3" in extracted_data:  # 征信报告
            credit_data = extracted_data["3"]
            loans = credit_data.get("loans", {})
            credit_debt = str(loans.get("total_balance", 0))
        
        if not application_debt and not credit_debt:
            return VerificationResult(
                result="无法判断",
                details="缺少负债信息",
                evidence=[]
            )
        
        # 简单的一致性检查
        if application_debt and credit_debt:
            return VerificationResult(
                result="符合",
                details="申请表与征信报告均包含负债信息",
                evidence=[f"申请表负债: {application_debt}", f"征信报告负债: {credit_debt}"]
            )
        
        return VerificationResult(
            result="无法判断",
            details="负债信息不完整",
            evidence=[]
        )

    def _extract_name_from_document(self, doc_data: Dict[str, Any], doc_type: str) -> str:
        """从文档数据中提取姓名"""
        if doc_type == "1":  # 申请表
            return doc_data.get("personal_info", {}).get("name", "")
        elif doc_type == "2":  # 身份证
            return doc_data.get("front_info", {}).get("name", "")
        elif doc_type == "3":  # 征信报告
            return doc_data.get("personal_info", {}).get("name", "")
        elif doc_type == "4":  # 教育背景
            return doc_data.get("personal_info", {}).get("name", "")
        elif doc_type == "5":  # 邮政履历
            return doc_data.get("personal_info", {}).get("name", "")
        return ""

    def _extract_id_number_from_document(self, doc_data: Dict[str, Any], doc_type: str) -> str:
        """从文档数据中提取身份证号"""
        if doc_type == "1":  # 申请表
            return doc_data.get("personal_info", {}).get("id_number", "")
        elif doc_type == "2":  # 身份证
            return doc_data.get("front_info", {}).get("id_number", "")
        elif doc_type == "3":  # 征信报告
            return doc_data.get("personal_info", {}).get("id_number", "")
        elif doc_type == "4":  # 教育背景
            return doc_data.get("personal_info", {}).get("id_number", "")
        elif doc_type == "5":  # 邮政履历
            return doc_data.get("personal_info", {}).get("id_number", "")
        return ""

    def _normalize_education_level(self, education: str) -> str:
        """标准化学历层次"""
        education = education.lower()
        if any(keyword in education for keyword in ["博士", "phd", "doctor"]):
            return "博士"
        elif any(keyword in education for keyword in ["硕士", "master", "研究生"]):
            return "硕士"
        elif any(keyword in education for keyword in ["本科", "学士", "bachelor"]):
            return "本科"
        elif any(keyword in education for keyword in ["大专", "专科", "college"]):
            return "大专"
        elif any(keyword in education for keyword in ["高中", "中专", "技校"]):
            return "高中"
        return education

    def _verify_application_form_validity(self, app_data: Dict[str, Any]) -> VerificationResult:
        """验证申请表有效性"""
        validity_check = app_data.get("validity_check", {})

        if validity_check.get("is_valid", False):
            return VerificationResult(
                result="符合",
                details="申请表填写完整且有效",
                evidence=["表格签名完整", "日期填写正确", "必填项完整"]
            )
        else:
            issues = validity_check.get("issues", [])
            return VerificationResult(
                result="不符合",
                details="申请表存在问题",
                evidence=issues
            )

    def _verify_id_card_validity(self, id_data: Dict[str, Any]) -> VerificationResult:
        """验证身份证有效性"""
        validation = id_data.get("validation", {})

        if validation.get("is_valid", False):
            return VerificationResult(
                result="符合",
                details="身份证信息有效",
                evidence=["身份证号格式正确", "校验位验证通过"]
            )
        else:
            issues = validation.get("issues", [])
            return VerificationResult(
                result="不符合",
                details="身份证信息无效",
                evidence=issues
            )

    def _verify_credit_report_timeliness(self, credit_data: Dict[str, Any]) -> VerificationResult:
        """验证征信报告时效性"""
        timeliness_check = credit_data.get("timeliness_check", {})

        if timeliness_check.get("is_within_30_days", False):
            query_date = timeliness_check.get("query_date", "")
            return VerificationResult(
                result="符合",
                details="征信报告在30天有效期内",
                evidence=[f"查询日期: {query_date}"]
            )
        else:
            issues = timeliness_check.get("issues", [])
            return VerificationResult(
                result="不符合",
                details="征信报告超出有效期",
                evidence=issues
            )

    def _verify_education_certificate(self, edu_data: Dict[str, Any]) -> VerificationResult:
        """验证学历证明"""
        qualification_check = edu_data.get("qualification_check", {})

        if qualification_check.get("certificate_valid", False):
            return VerificationResult(
                result="符合",
                details="学历证明有效",
                evidence=["证书信息完整", "学历层次明确"]
            )
        else:
            issues = qualification_check.get("issues", [])
            return VerificationResult(
                result="不符合",
                details="学历证明存在问题",
                evidence=issues
            )

    def _load_verification_rules(self) -> Dict[str, Any]:
        """加载验证规则"""
        return {
            "education_requirement": "大专及以上",
            "credit_report_validity_days": 30,
            "required_documents": ["申请表", "身份证", "征信报告", "教育背景"],
            "identity_fields": ["name", "id_number"]
        }
