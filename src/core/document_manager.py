"""
文档管理器
负责识别、分类和管理6种标准文档类型
"""

import os
import re
import logging
from typing import Dict, List, Optional, Tuple
from src.schemas.audit_result_schema import DocumentInfo

logger = logging.getLogger(__name__)


class DocumentManager:
    """6种文档统一管理器"""
    
    # 标准文档类型定义
    DOCUMENT_TYPES = {
        "1": {"name": "申请表", "keywords": ["申请", "application"], "required": True},
        "2": {"name": "身份证", "keywords": ["身份证", "id", "身份"], "required": True},
        "3": {"name": "征信报告", "keywords": ["征信", "credit", "信用", "报告"], "required": True},
        "4": {"name": "教育背景", "keywords": ["教育", "学历", "education", "背景"], "required": True},
        "5": {"name": "邮政履历", "keywords": ["履历", "邮政", "工作", "career"], "required": True},
        "6": {"name": "执行网信息", "keywords": ["执行", "execution", "公开", "网"], "required": True}
    }
    
    def __init__(self, applicant_dir: str):
        self.applicant_dir = applicant_dir
        self.documents: Dict[str, DocumentInfo] = {}
        self._scan_documents()
    
    def _scan_documents(self):
        """扫描并分类文档"""
        if not os.path.exists(self.applicant_dir):
            logger.error(f"申请人目录不存在: {self.applicant_dir}")
            return
        
        pdf_files = [f for f in os.listdir(self.applicant_dir) 
                    if f.lower().endswith('.pdf')]
        
        logger.info(f"发现PDF文件: {pdf_files}")
        
        # 首先尝试基于文件名编号识别
        for pdf_file in pdf_files:
            doc_type = self._identify_by_number(pdf_file)
            if doc_type:
                self._add_document(doc_type, pdf_file)
                continue
            
            # 如果编号识别失败，尝试关键词识别
            doc_type = self._identify_by_keywords(pdf_file)
            if doc_type:
                self._add_document(doc_type, pdf_file)
            else:
                logger.warning(f"无法识别文档类型: {pdf_file}")
        
        # 检查必需文档是否完整
        self._check_required_documents()
    
    def _identify_by_number(self, filename: str) -> Optional[str]:
        """基于文件名编号识别文档类型"""
        # 匹配模式：数字开头，如 "1申请表.pdf", "2身份证.pdf"
        pattern = r'^(\d+)'
        match = re.match(pattern, filename)
        if match:
            number = match.group(1)
            if number in self.DOCUMENT_TYPES:
                return number
        return None
    
    def _identify_by_keywords(self, filename: str) -> Optional[str]:
        """基于关键词识别文档类型"""
        filename_lower = filename.lower()
        
        for doc_type, config in self.DOCUMENT_TYPES.items():
            for keyword in config["keywords"]:
                if keyword in filename_lower:
                    return doc_type
        return None
    
    def _add_document(self, doc_type: str, filename: str):
        """添加文档信息"""
        file_path = os.path.join(self.applicant_dir, filename)
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
        
        doc_info = DocumentInfo(
            file_name=filename,
            file_type=f"{doc_type}-{self.DOCUMENT_TYPES[doc_type]['name']}",
            status="存在",
            pages=0,  # 后续通过PDF解析获取
            size_mb=round(file_size, 2)
        )
        
        self.documents[doc_type] = doc_info
        logger.info(f"识别文档: {filename} -> {doc_info.file_type}")
    
    def _check_required_documents(self):
        """检查必需文档是否完整"""
        missing_docs = []
        for doc_type, config in self.DOCUMENT_TYPES.items():
            if config["required"] and doc_type not in self.documents:
                missing_doc = DocumentInfo(
                    file_name="",
                    file_type=f"{doc_type}-{config['name']}",
                    status="缺失"
                )
                self.documents[doc_type] = missing_doc
                missing_docs.append(config["name"])
        
        if missing_docs:
            logger.warning(f"缺失必需文档: {missing_docs}")
    
    def get_document_by_type(self, doc_type: str) -> Optional[DocumentInfo]:
        """根据类型获取文档信息"""
        return self.documents.get(doc_type)
    
    def get_document_path(self, doc_type: str) -> Optional[str]:
        """获取文档完整路径"""
        doc_info = self.get_document_by_type(doc_type)
        if doc_info and doc_info.status == "存在":
            return os.path.join(self.applicant_dir, doc_info.file_name)
        return None
    
    def get_all_documents(self) -> List[DocumentInfo]:
        """获取所有文档信息"""
        return list(self.documents.values())
    
    def get_existing_documents(self) -> Dict[str, str]:
        """获取存在的文档路径映射"""
        existing = {}
        for doc_type, doc_info in self.documents.items():
            if doc_info.status == "存在":
                existing[doc_type] = os.path.join(self.applicant_dir, doc_info.file_name)
        return existing
    
    def is_complete(self) -> bool:
        """检查文档是否完整"""
        for doc_type, config in self.DOCUMENT_TYPES.items():
            if config["required"]:
                doc_info = self.documents.get(doc_type)
                if not doc_info or doc_info.status != "存在":
                    return False
        return True
    
    def get_completeness_report(self) -> Dict[str, any]:
        """获取文档完整性报告"""
        total_required = sum(1 for config in self.DOCUMENT_TYPES.values() if config["required"])
        existing_required = sum(1 for doc_type, config in self.DOCUMENT_TYPES.items() 
                              if config["required"] and 
                              self.documents.get(doc_type, {}).get("status") == "存在")
        
        return {
            "is_complete": self.is_complete(),
            "total_required": total_required,
            "existing_required": existing_required,
            "completion_rate": existing_required / total_required if total_required > 0 else 0,
            "missing_documents": [
                self.DOCUMENT_TYPES[doc_type]["name"] 
                for doc_type, config in self.DOCUMENT_TYPES.items()
                if config["required"] and 
                self.documents.get(doc_type, {}).get("status") != "存在"
            ]
        }
