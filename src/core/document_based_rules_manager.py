"""
基于原文的规则管理器
保持文档完整内容，用于LLM上下文传递
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional
from docx import Document

logger = logging.getLogger(__name__)


class DocumentBasedRulesManager:
    """基于原文的规则管理器 - 保持文档完整内容"""
    
    def __init__(self, rules_dir: str = "规则文档"):
        self.rules_dir = rules_dir
        self.documents = {}
        self.audit_structure = {}
        self.load_all_documents()
    
    def load_all_documents(self):
        """加载所有规则文档的完整内容"""
        try:
            doc_files = {
                "audit_report_rules": "审核报告规则(初稿）0725.docx",
                "credit_classification": "人行征信系统个人信用信息分类标准.docx", 
                "audit_requirements": "关于明确代理营业机构负责人任职资格征信审核要点的通知.docx"
            }
            
            for key, filename in doc_files.items():
                file_path = os.path.join(self.rules_dir, filename)
                if os.path.exists(file_path):
                    self.documents[key] = self._load_document_content(file_path)
                    logger.info(f"已加载文档: {filename}")
                else:
                    logger.warning(f"文档不存在: {file_path}")
            
            # 解析审核报告规则的结构（用于确定需要审核的内容）
            if "audit_report_rules" in self.documents:
                self.audit_structure = self._parse_audit_structure()
            
            logger.info("所有规则文档加载完成")
            
        except Exception as e:
            logger.error(f"加载规则文档失败: {e}")
            raise
    
    def _load_document_content(self, file_path: str) -> str:
        """加载Word文档的完整内容"""
        try:
            doc = Document(file_path)
            content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content.append(paragraph.text.strip())
            
            # 处理表格内容
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        content.append(" | ".join(row_text))
            
            return "\n".join(content)
            
        except Exception as e:
            logger.error(f"读取文档失败 {file_path}: {e}")
            return ""
    
    def _parse_audit_structure(self) -> Dict[str, Any]:
        """解析审核报告规则，提取需要审核的内容结构"""
        # 这里使用简单的关键词匹配来识别审核结构
        # 实际应用中可以使用LLM来做更智能的解析
        
        audit_content = self.documents.get("audit_report_rules", "")
        
        structure = {
            "basic_verification": {
                "name": "基本信息核验（硬性审核）",
                "required_materials": ["申请表", "身份证", "征信报告", "教育背景", "邮政履历", "执行网信息"],
                "supplementary_materials": [],  # 补充材料会动态识别
                "verification_items": [
                    "材料完整性检查",
                    "身份信息一致性验证", 
                    "学历证明验证",
                    "材料有效性检查"
                ]
            },
            "risk_assessment": {
                "name": "风险评估（软性审核，自动化为辅）",
                "assessment_items": [
                    "信用卡情况",
                    "负债情况", 
                    "非银机构贷款",
                    "经营性贷款",
                    "担保情况",
                    "征信禁入/瑕疵",
                    "异常信息"
                ],
                "output_format": "表格形式：核查类型 | 结果 | 明细数据"
            }
        }
        
        # 从文档中识别补充材料类型
        if "谈话记录" in audit_content:
            structure["basic_verification"]["supplementary_materials"].append("谈话记录")
        if "岗位证书" in audit_content:
            structure["basic_verification"]["supplementary_materials"].append("岗位证书")
        if "工作证明" in audit_content:
            structure["basic_verification"]["supplementary_materials"].append("工作证明")
        
        return structure
    
    def get_full_rules_content(self) -> str:
        """获取所有规则文档的完整内容"""
        context = ""

        # 添加所有已加载的文档内容
        if "audit_report_rules" in self.documents:
            context += f"【审核报告规则】\n{self.documents['audit_report_rules']}\n\n"

        if "credit_classification" in self.documents:
            context += f"【征信分类标准】\n{self.documents['credit_classification']}\n\n"

        if "audit_requirements" in self.documents:
            context += f"【审核要点通知】\n{self.documents['audit_requirements']}\n\n"

        return context

    def get_assessment_context(self, assessment_type: str) -> str:
        """根据评估类型返回相关的完整文档内容"""
        context = ""

        # 所有评估都包含审核报告规则
        if "audit_report_rules" in self.documents:
            context += f"【审核报告规则】\n{self.documents['audit_report_rules']}\n\n"

        # 征信相关评估包含分类标准和审核要点
        if assessment_type in ["征信风险", "信用评估", "负债评估", "风险评估"]:
            if "credit_classification" in self.documents:
                context += f"【征信分类标准】\n{self.documents['credit_classification']}\n\n"
            if "audit_requirements" in self.documents:
                context += f"【审核要点通知】\n{self.documents['audit_requirements']}\n\n"

        return context
    
    def get_audit_structure(self) -> Dict[str, Any]:
        """获取审核结构"""
        return self.audit_structure
    
    def get_required_materials(self) -> List[str]:
        """获取必需材料列表"""
        basic_verification = self.audit_structure.get("basic_verification", {})
        required = basic_verification.get("required_materials", [])
        supplementary = basic_verification.get("supplementary_materials", [])
        return required + supplementary
    
    def get_verification_items(self) -> List[str]:
        """获取验证项目列表"""
        basic_verification = self.audit_structure.get("basic_verification", {})
        return basic_verification.get("verification_items", [])
    
    def get_risk_assessment_items(self) -> List[str]:
        """获取风险评估项目列表"""
        risk_assessment = self.audit_structure.get("risk_assessment", {})
        return risk_assessment.get("assessment_items", [])
    
    def identify_supplementary_materials(self, material_list: List[str]) -> List[str]:
        """识别用户提交的补充材料"""
        standard_materials = ["申请表", "身份证", "征信报告", "教育背景", "邮政履历", "执行网信息"]
        supplementary = []
        
        for material in material_list:
            # 简单的关键词匹配识别补充材料
            if not any(std in material for std in standard_materials):
                if "谈话" in material or "记录" in material:
                    supplementary.append("谈话记录")
                elif "证书" in material or "资格" in material:
                    supplementary.append("岗位证书")
                elif "工作" in material or "证明" in material:
                    supplementary.append("工作证明")
                else:
                    supplementary.append(material)
        
        return list(set(supplementary))  # 去重
    
    def build_full_context(self, assessment_type: str, extracted_data: Dict[str, Any]) -> str:
        """构建完整的评估上下文"""
        context = ""

        # 添加规则文档内容
        context += self.get_assessment_context(assessment_type)

        # 添加提取的材料内容
        context += "【申请人提交材料】\n"
        for doc_type, doc_data in extracted_data.items():
            type_name = doc_data.get("type_name", f"文档{doc_type}")
            content = doc_data.get("content", {})

            context += f"\n=== {type_name} ===\n"

            if isinstance(content, dict):
                # 递归处理嵌套字典
                context += self._format_dict_content(content, indent=0)
            else:
                context += f"{content}\n"

            context += "\n"

        context += f"请严格按照上述规则文档对【{assessment_type}】进行评估，并提供详细的评估依据。\n"

        return context

    def _format_dict_content(self, content: Dict[str, Any], indent: int = 0) -> str:
        """格式化字典内容"""
        formatted = ""
        indent_str = "  " * indent

        for key, value in content.items():
            if isinstance(value, dict):
                formatted += f"{indent_str}{key}:\n"
                formatted += self._format_dict_content(value, indent + 1)
            elif isinstance(value, list):
                formatted += f"{indent_str}{key}:\n"
                for item in value:
                    if isinstance(item, dict):
                        formatted += self._format_dict_content(item, indent + 1)
                    else:
                        formatted += f"{indent_str}  - {item}\n"
            else:
                formatted += f"{indent_str}{key}: {value}\n"

        return formatted
