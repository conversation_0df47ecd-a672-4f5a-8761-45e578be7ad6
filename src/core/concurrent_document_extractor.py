"""
并发文档提取器
实现单用户多文档的并发提取
"""

import os
import time
import logging
from typing import Dict, Any, List, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass

from config import DOCUMENT_CONFIG
from src.core.enhanced_document_manager import EnhancedDocumentManager
from src.extractors.universal_extractor import UniversalExtractor

logger = logging.getLogger(__name__)


@dataclass
class ExtractionTask:
    """文档提取任务"""
    doc_type: str
    file_path: str
    type_name: str
    extractor_class: type


class ConcurrentDocumentExtractor:
    """并发文档提取器"""
    
    def __init__(self):
        # 获取并发配置
        vlm_config = DOCUMENT_CONFIG.get("vlm_concurrency", {})
        self.document_workers = vlm_config.get("document_level_workers", 6)
        self.batch_delay = vlm_config.get("api_rate_limit", {}).get("batch_delay_seconds", 1.0)
        self.concurrent_limit = vlm_config.get("api_rate_limit", {}).get("concurrent_limit", 6)
        
        # 文档类型名称映射（用于日志和标识）
        self.document_type_names = {
            "1": "申请表",
            "2": "身份证",
            "3": "征信报告",
            "4": "教育背景",
            "5": "邮政履历",
            "6": "执行网信息",
            "7": "其他文档类型7",
            "8": "其他文档类型8",
            "9": "其他文档类型9",
            "10": "补充材料",
            "supplementary": "补充材料"
        }
    
    def extract_all_documents(self, applicant_dir: str, use_cache: bool = True) -> Dict[str, Any]:
        """并发提取申请人的所有文档"""
        logger.info(f"开始并发提取文档: {applicant_dir}")
        
        # 1. 分析文档
        doc_manager = EnhancedDocumentManager(applicant_dir)
        doc_list = doc_manager.get_document_list_for_extraction()
        
        if not doc_list:
            logger.warning(f"未找到可提取的文档: {applicant_dir}")
            return {}
        
        # 2. 创建提取任务
        tasks = self._create_extraction_tasks(doc_list)
        
        # 3. 并发执行提取
        results = self._execute_concurrent_extraction(tasks, use_cache)
        
        logger.info(f"文档提取完成: {len(results)} 个文档")
        return results
    
    def _create_extraction_tasks(self, doc_list: List[Tuple[str, str, str]]) -> List[ExtractionTask]:
        """创建提取任务列表"""
        tasks = []
        
        for doc_type, file_path, type_name in doc_list:
            # 获取文档类型名称
            document_type_name = self.document_type_names.get(doc_type, f"文档类型{doc_type}")

            # 创建任务，使用通用提取器
            task = ExtractionTask(
                doc_type=doc_type,
                file_path=file_path,
                type_name=type_name,
                extractor_class=lambda doc_name=document_type_name: UniversalExtractor(document_type=doc_name)
            )
            tasks.append(task)
        
        logger.info(f"创建了 {len(tasks)} 个通用提取任务")
        return tasks
    
    def _execute_concurrent_extraction(self, tasks: List[ExtractionTask], use_cache: bool) -> Dict[str, Any]:
        """并发执行文档提取"""
        results = {}
        
        # 如果任务数量较少，使用串行处理
        if len(tasks) <= 2:
            logger.info("任务数量较少，使用串行处理")
            for task in tasks:
                result = self._extract_single_document(task, use_cache)
                if result:
                    results[task.doc_type] = {
                        "type_name": task.type_name,
                        "filename": os.path.basename(task.file_path),
                        "content": result
                    }
            return results
        
        # 多任务并发处理 - 限制并发数量避免超过API限制
        actual_workers = min(self.document_workers, len(tasks), self.concurrent_limit)
        logger.info(f"使用 {actual_workers} 个线程并发处理 {len(tasks)} 个文档 (并发限制: {self.concurrent_limit})")

        with ThreadPoolExecutor(max_workers=actual_workers) as executor:
            # 提交所有任务
            future_to_task = {
                executor.submit(self._extract_single_document, task, use_cache): task
                for task in tasks
            }
            
            # 收集结果
            completed_count = 0
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                completed_count += 1
                
                try:
                    result = future.result(timeout=300)  # 5分钟超时
                    
                    if result and "error" not in result:
                        results[task.doc_type] = {
                            "type_name": task.type_name,
                            "filename": os.path.basename(task.file_path),
                            "content": result
                        }
                        logger.info(f"✅ 文档提取完成 ({completed_count}/{len(tasks)}): {task.type_name}")
                    else:
                        logger.error(f"❌ 文档提取失败: {task.type_name} - {result.get('error', '未知错误')}")
                
                except Exception as e:
                    logger.error(f"❌ 文档提取异常: {task.type_name} - {e}")
                
                # 添加批次间延时，避免API频率限制
                if completed_count < len(tasks):
                    time.sleep(self.batch_delay)
        
        return results
    
    def _extract_single_document(self, task: ExtractionTask, use_cache: bool) -> Optional[Dict[str, Any]]:
        """提取单个文档"""
        try:
            # 创建提取器实例
            extractor = task.extractor_class()
            
            # 执行提取
            logger.debug(f"开始提取: {task.type_name}")
            result = extractor.extract_from_pdf(task.file_path, use_cache=use_cache)
            
            return result
            
        except Exception as e:
            logger.error(f"文档提取失败 {task.type_name}: {e}")
            return {"error": str(e)}
    
    def get_extraction_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """获取提取结果摘要"""
        summary = {
            "total_documents": len(results),
            "successful_extractions": 0,
            "failed_extractions": 0,
            "document_types": [],
            "extraction_details": {}
        }
        
        for doc_type, doc_data in results.items():
            doc_content = doc_data.get("content", {})
            
            if "error" in doc_content:
                summary["failed_extractions"] += 1
                summary["extraction_details"][doc_type] = {
                    "status": "失败",
                    "error": doc_content["error"]
                }
            else:
                summary["successful_extractions"] += 1
                summary["document_types"].append(doc_data["type_name"])
                
                # 统计提取的字段数量
                field_count = len([k for k, v in doc_content.items()
                                 if k not in ["raw_content", "source_file", "pages_processed"] and v])
                
                summary["extraction_details"][doc_type] = {
                    "status": "成功",
                    "type_name": doc_data["type_name"],
                    "filename": doc_data["filename"],
                    "field_count": field_count,
                    "pages_processed": doc_content.get("pages_processed", 0)
                }
        
        return summary
