"""
审核结果JSON标准模式定义
定义审核服务返回的标准化JSON格式
"""

from typing import Dict, List, Any, Optional, Literal
from dataclasses import dataclass, asdict
import json


@dataclass
class DocumentInfo:
    """文档信息"""
    file_name: str
    file_type: str  # 1-申请表, 2-身份证, 3-征信报告, 4-教育背景, 5-邮政履历, 6-执行网信息
    status: str  # "存在" | "缺失" | "无法识别"
    pages: int = 0
    size_mb: float = 0.0


@dataclass
class VerificationResult:
    """核验结果"""
    result: Literal["符合", "不符合", "无法判断"]
    details: str
    evidence: List[str] = None
    
    def __post_init__(self):
        if self.evidence is None:
            self.evidence = []


@dataclass
class IdentityConsistency:
    """身份一致性检查"""
    name_consistency: VerificationResult
    id_card_consistency: VerificationResult
    education_consistency: VerificationResult
    career_consistency: VerificationResult
    debt_consistency: VerificationResult


@dataclass
class BasicVerification:
    """基本信息核验（硬性审核）"""
    material_validity: Dict[str, VerificationResult]  # 材料有效性
    information_consistency: IdentityConsistency  # 信息一致性
    education_verification: VerificationResult  # 学历证明


@dataclass
class RiskAssessment:
    """风险评估（软性审核）"""
    credit_card_status: VerificationResult  # 信用卡情况
    debt_status: VerificationResult  # 负债情况
    non_bank_loans: VerificationResult  # 非银机构贷款
    business_loans: VerificationResult  # 经营性贷款
    guarantee_status: VerificationResult  # 担保情况
    credit_blacklist: VerificationResult  # 征信禁入/瑕疵
    abnormal_info: VerificationResult  # 异常信息


@dataclass
class FinalAssessment:
    """最终评估"""
    qualification_result: Literal["符合条件", "不符合条件", "需要进一步审核"]
    risk_level: Literal["低风险", "中风险", "高风险", "严重风险"]
    confidence_score: float  # 0.0-1.0
    key_findings: List[str]
    summary: str


@dataclass
class NextAction:
    """下一步操作建议"""
    action_type: Literal["通过", "拒绝", "补充材料", "人工复核", "实地调研"]
    priority: Literal["高", "中", "低"]
    description: str
    deadline_days: Optional[int] = None


@dataclass
class AuditResult:
    """完整审核结果"""
    # 基本信息
    applicant_name: str
    applicant_id: str
    audit_time: str
    audit_version: str = "2.0"
    
    # 文档信息
    documents: List[DocumentInfo]
    
    # 审核结果
    basic_verification: BasicVerification
    risk_assessment: RiskAssessment
    final_assessment: FinalAssessment
    next_actions: List[NextAction]
    
    # 原始数据（可选，用于调试）
    raw_data: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)
    
    def to_json(self, indent: int = 2) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=indent)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AuditResult':
        """从字典创建实例"""
        # 这里需要递归转换嵌套的dataclass
        # 简化实现，实际使用时可能需要更复杂的反序列化逻辑
        return cls(**data)


def create_empty_audit_result(applicant_name: str, applicant_id: str) -> AuditResult:
    """创建空的审核结果模板"""
    from datetime import datetime

    return AuditResult(
        applicant_name=applicant_name,
        applicant_id=applicant_id,
        audit_time=datetime.now().isoformat(),
        documents=[],
        basic_verification=BasicVerification(
            material_validity={},
            information_consistency=IdentityConsistency(
                name_consistency=VerificationResult("无法判断", "未检查"),
                id_card_consistency=VerificationResult("无法判断", "未检查"),
                education_consistency=VerificationResult("无法判断", "未检查"),
                career_consistency=VerificationResult("无法判断", "未检查"),
                debt_consistency=VerificationResult("无法判断", "未检查")
            ),
            education_verification=VerificationResult("无法判断", "未检查")
        ),
        risk_assessment=RiskAssessment(
            credit_card_status=VerificationResult("无法判断", "未评估"),
            debt_status=VerificationResult("无法判断", "未评估"),
            non_bank_loans=VerificationResult("无法判断", "未评估"),
            business_loans=VerificationResult("无法判断", "未评估"),
            guarantee_status=VerificationResult("无法判断", "未评估"),
            credit_blacklist=VerificationResult("无法判断", "未评估"),
            abnormal_info=VerificationResult("无法判断", "未评估")
        ),
        final_assessment=FinalAssessment(
            qualification_result="需要进一步审核",
            risk_level="中风险",
            confidence_score=0.0,
            key_findings=[],
            summary="审核未完成"
        ),
        next_actions=[]
    )
