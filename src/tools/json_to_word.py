"""
JSON到Word报告生成工具
将审核结果JSON转换为格式化的Word文档
"""

import os
import json
import logging
from typing import Dict, Any
from datetime import datetime
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn

logger = logging.getLogger(__name__)


class JSONToWordConverter:
    """JSON到Word转换器"""
    
    def __init__(self):
        self.doc = None
    
    def convert_audit_result(self, json_file_path: str, output_path: str = None) -> str:
        """转换审核结果JSON为Word文档"""
        try:
            # 读取JSON文件
            with open(json_file_path, 'r', encoding='utf-8') as f:
                audit_data = json.load(f)
            
            # 创建Word文档
            self.doc = Document()
            
            # 设置文档样式
            self._setup_document_styles()
            
            # 生成报告内容
            self._add_header(audit_data)
            self._add_basic_info(audit_data)
            self._add_document_completeness(audit_data)
            self._add_identity_verification(audit_data)
            self._add_material_verification(audit_data)
            self._add_risk_assessment(audit_data)
            self._add_final_assessment(audit_data)
            self._add_recommendations(audit_data)
            self._add_footer()
            
            # 保存文档
            if not output_path:
                applicant_name = audit_data.get("applicant_name", "未知")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"reports/{applicant_name}_审核报告_{timestamp}.docx"
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            self.doc.save(output_path)
            logger.info(f"Word报告生成成功: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Word报告生成失败: {e}")
            raise
    
    def _setup_document_styles(self):
        """设置文档样式"""
        # 设置默认字体
        style = self.doc.styles['Normal']
        font = style.font
        font.name = '宋体'
        font.size = Pt(12)
    
    def _add_header(self, audit_data: Dict[str, Any]):
        """添加文档标题"""
        # 主标题
        title = self.doc.add_heading('代理营业机构负责人任职资格审核报告', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 副标题
        applicant_name = audit_data.get("applicant_name", "未知")
        subtitle = self.doc.add_heading(f'申请人：{applicant_name}', level=2)
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 审核日期
        audit_date = audit_data.get("audit_date", datetime.now().strftime("%Y-%m-%d"))
        date_para = self.doc.add_paragraph(f'审核日期：{audit_date}')
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        self.doc.add_paragraph()  # 空行
    
    def _add_basic_info(self, audit_data: Dict[str, Any]):
        """添加基本信息"""
        self.doc.add_heading('一、申请人基本信息', level=1)
        
        # 从提取的数据中获取基本信息
        extracted_data = audit_data.get("extracted_data", {})
        
        # 创建基本信息表格
        table = self.doc.add_table(rows=1, cols=2)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        # 表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '项目'
        hdr_cells[1].text = '内容'
        
        # 添加信息行
        info_items = []
        
        # 从申请表获取信息
        if "1" in extracted_data:
            app_data = extracted_data["1"]
            personal_info = app_data.get("personal_info", {})
            info_items.extend([
                ("姓名", personal_info.get("name", "未提供")),
                ("身份证号", personal_info.get("id_number", "未提供")),
                ("性别", personal_info.get("gender", "未提供")),
                ("出生日期", personal_info.get("birth_date", "未提供")),
                ("联系电话", personal_info.get("phone", "未提供"))
            ])
            
            education_info = app_data.get("education_info", {})
            if education_info:
                info_items.append(("学历", education_info.get("highest_degree", "未提供")))
        
        # 从身份证获取信息
        if "2" in extracted_data:
            id_data = extracted_data["2"]
            front_info = id_data.get("front_info", {})
            if front_info and not any(item[0] == "姓名" for item in info_items):
                info_items.extend([
                    ("姓名", front_info.get("name", "未提供")),
                    ("身份证号", front_info.get("id_number", "未提供"))
                ])
        
        # 添加表格行
        for item, content in info_items:
            row_cells = table.add_row().cells
            row_cells[0].text = item
            row_cells[1].text = str(content)
        
        self.doc.add_paragraph()
    
    def _add_document_completeness(self, audit_data: Dict[str, Any]):
        """添加文档完整性检查"""
        self.doc.add_heading('二、申请材料完整性检查', level=1)
        
        document_completeness = audit_data.get("document_completeness", {})
        
        if "error" in document_completeness:
            para = self.doc.add_paragraph(f"文档扫描错误：{document_completeness['error']}")
            return
        
        # 文档完整性表格
        table = self.doc.add_table(rows=1, cols=3)
        table.style = 'Table Grid'
        
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '文档类型'
        hdr_cells[1].text = '提交状态'
        hdr_cells[2].text = '文件名'
        
        documents = document_completeness.get("documents", {})
        required_docs = {
            "1": "申请表",
            "2": "身份证",
            "3": "征信报告",
            "4": "教育背景",
            "5": "邮政履历",
            "6": "执行网信息"
        }
        
        for doc_type, doc_name in required_docs.items():
            row_cells = table.add_row().cells
            row_cells[0].text = doc_name
            
            if doc_type in documents:
                row_cells[1].text = "✓ 已提交"
                row_cells[2].text = os.path.basename(documents[doc_type])
            else:
                row_cells[1].text = "✗ 缺失"
                row_cells[2].text = "-"
        
        # 完整性总结
        total_docs = len(required_docs)
        submitted_docs = len(documents)
        completeness_rate = (submitted_docs / total_docs) * 100
        
        summary_para = self.doc.add_paragraph(
            f"\n材料完整性：{submitted_docs}/{total_docs} ({completeness_rate:.1f}%)"
        )
        
        self.doc.add_paragraph()
    
    def _add_identity_verification(self, audit_data: Dict[str, Any]):
        """添加身份一致性验证"""
        self.doc.add_heading('三、身份一致性验证', level=1)
        
        identity_consistency = audit_data.get("identity_consistency")
        if not identity_consistency:
            self.doc.add_paragraph("身份一致性验证数据缺失")
            return
        
        # 验证项目表格
        table = self.doc.add_table(rows=1, cols=3)
        table.style = 'Table Grid'
        
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '验证项目'
        hdr_cells[1].text = '验证结果'
        hdr_cells[2].text = '详细说明'
        
        verification_items = [
            ("姓名一致性", identity_consistency.get("name_consistency")),
            ("身份证号一致性", identity_consistency.get("id_card_consistency")),
            ("学历信息一致性", identity_consistency.get("education_consistency")),
            ("工作经历一致性", identity_consistency.get("career_consistency")),
            ("负债信息一致性", identity_consistency.get("debt_consistency"))
        ]
        
        for item_name, verification_result in verification_items:
            if verification_result:
                row_cells = table.add_row().cells
                row_cells[0].text = item_name
                row_cells[1].text = verification_result.get("result", "未验证")
                row_cells[2].text = verification_result.get("details", "")
        
        self.doc.add_paragraph()
    
    def _add_material_verification(self, audit_data: Dict[str, Any]):
        """添加材料有效性验证"""
        self.doc.add_heading('四、申请材料有效性验证', level=1)
        
        basic_verification = audit_data.get("basic_verification")
        if not basic_verification:
            self.doc.add_paragraph("材料验证数据缺失")
            return
        
        # 材料有效性表格
        material_validity = basic_verification.get("material_validity", {})
        if material_validity:
            table = self.doc.add_table(rows=1, cols=3)
            table.style = 'Table Grid'
            
            hdr_cells = table.rows[0].cells
            hdr_cells[0].text = '材料类型'
            hdr_cells[1].text = '有效性'
            hdr_cells[2].text = '验证详情'
            
            material_names = {
                "application_form": "申请表",
                "id_card": "身份证",
                "credit_report": "征信报告",
                "education_certificate": "学历证明"
            }
            
            for material_type, material_name in material_names.items():
                if material_type in material_validity:
                    validity = material_validity[material_type]
                    row_cells = table.add_row().cells
                    row_cells[0].text = material_name
                    row_cells[1].text = validity.get("result", "未验证")
                    row_cells[2].text = validity.get("details", "")
        
        # 学历要求验证
        education_req = basic_verification.get("education_requirement")
        if education_req:
            self.doc.add_paragraph("\n学历要求验证：")
            para = self.doc.add_paragraph(
                f"验证结果：{education_req.get('result', '未验证')}\n"
                f"详细说明：{education_req.get('details', '')}"
            )
        
        self.doc.add_paragraph()
    
    def _add_risk_assessment(self, audit_data: Dict[str, Any]):
        """添加风险评估 - 按新规则表格格式"""
        self.doc.add_heading('二、风险评估（软性审核，自动化为辅）', level=1)

        risk_assessment = audit_data.get("risk_assessment")
        if not risk_assessment:
            self.doc.add_paragraph("风险评估数据缺失")
            return

        # 按新规则创建风险评估表格
        table = self.doc.add_table(rows=1, cols=3)
        table.style = 'Table Grid'

        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '核查类型'
        hdr_cells[1].text = '结果'
        hdr_cells[2].text = '明细数据'

        # 获取征信风险和执行风险数据
        credit_risk = risk_assessment.get("credit_risk", {})
        execution_risk = risk_assessment.get("execution_risk", {})

        # 按新规则定义的核查类型
        check_items = [
            ("信用卡情况", credit_risk.get("result", "未评估"), self._extract_credit_card_details(credit_risk)),
            ("负债情况", credit_risk.get("result", "未评估"), self._extract_debt_details(credit_risk)),
            ("非银机构贷款", credit_risk.get("result", "未评估"), self._extract_non_bank_loan_details(credit_risk)),
            ("经营性贷款", credit_risk.get("result", "未评估"), self._extract_business_loan_details(credit_risk)),
            ("担保情况", credit_risk.get("result", "未评估"), self._extract_guarantee_details(credit_risk)),
            ("征信禁入/瑕疵", credit_risk.get("result", "未评估"), self._extract_credit_defect_details(credit_risk)),
            ("异常信息", execution_risk.get("result", "未评估"), self._extract_abnormal_info_details(execution_risk))
        ]

        for check_type, result, details in check_items:
            row_cells = table.add_row().cells
            row_cells[0].text = check_type
            row_cells[1].text = result  # 符合/不符合
            row_cells[2].text = details

        # 添加主要风险点说明
        self.doc.add_paragraph()
        self.doc.add_heading('（一）主要风险点', level=2)

        overall_risk = risk_assessment.get("overall_risk", {})
        evidence = overall_risk.get("evidence", [])
        if evidence:
            for item in evidence[:3]:  # 显示前3个主要风险点
                self.doc.add_paragraph(f"• {item}", style='List Bullet')
        else:
            self.doc.add_paragraph("• 未发现重大风险因素")

        # 添加征信风险说明
        self.doc.add_heading('（二）征信风险', level=2)
        self.doc.add_paragraph("仅客观记录负债金额、贷款频次等信息，不进行金额是否过大、贷款是否频繁等主观判断。")

        # 添加合规风险说明
        self.doc.add_heading('（三）合规风险', level=2)
        execution_evidence = execution_risk.get("evidence", [])
        if execution_evidence:
            for item in execution_evidence:
                self.doc.add_paragraph(f"• {item}", style='List Bullet')
        else:
            self.doc.add_paragraph("• 执行网查询结果清白")

        self.doc.add_paragraph()

    def _extract_credit_card_details(self, credit_risk: Dict[str, Any]) -> str:
        """提取信用卡情况明细"""
        evidence = credit_risk.get("evidence", [])
        card_details = [item for item in evidence if "信用卡" in item]
        return "; ".join(card_details) if card_details else "无信用卡记录"

    def _extract_debt_details(self, credit_risk: Dict[str, Any]) -> str:
        """提取负债情况明细"""
        evidence = credit_risk.get("evidence", [])
        debt_details = [item for item in evidence if "负债" in item or "还款" in item]
        return "; ".join(debt_details) if debt_details else "无负债记录"

    def _extract_non_bank_loan_details(self, credit_risk: Dict[str, Any]) -> str:
        """提取非银机构贷款明细"""
        evidence = credit_risk.get("evidence", [])
        loan_details = [item for item in evidence if "平台" in item or "借款" in item]
        return "; ".join(loan_details) if loan_details else "无非银机构贷款"

    def _extract_business_loan_details(self, credit_risk: Dict[str, Any]) -> str:
        """提取经营性贷款明细"""
        evidence = credit_risk.get("evidence", [])
        business_details = [item for item in evidence if "经营性贷款" in item]
        return "; ".join(business_details) if business_details else "无经营性贷款"

    def _extract_guarantee_details(self, credit_risk: Dict[str, Any]) -> str:
        """提取担保情况明细"""
        evidence = credit_risk.get("evidence", [])
        guarantee_details = [item for item in evidence if "担保" in item]
        return "; ".join(guarantee_details) if guarantee_details else "无担保记录"

    def _extract_credit_defect_details(self, credit_risk: Dict[str, Any]) -> str:
        """提取征信禁入/瑕疵明细"""
        evidence = credit_risk.get("evidence", [])
        defect_details = [item for item in evidence if "异常记录" in item or "逾期" in item]
        return "; ".join(defect_details) if defect_details else "无征信瑕疵"

    def _extract_abnormal_info_details(self, execution_risk: Dict[str, Any]) -> str:
        """提取异常信息明细"""
        evidence = execution_risk.get("evidence", [])
        abnormal_details = [item for item in evidence if "欠税" in item or "执行" in item or "电信" in item]
        return "; ".join(abnormal_details) if abnormal_details else "无异常信息"

    def _add_final_assessment(self, audit_data: Dict[str, Any]):
        """添加最终评估 - 不显示系统判断，仅提供操作建议"""
        self.doc.add_heading('三、审核建议', level=1)

        final_assessment = audit_data.get("final_assessment")
        next_action = audit_data.get("next_action")

        if not final_assessment and not next_action:
            self.doc.add_paragraph("评估数据缺失")
            return

        # 注意：根据新规则，不显示系统的通过/不通过判断
        # 只显示需要关注的问题和操作建议

        # 需要关注的问题
        if final_assessment:
            major_issues = final_assessment.get("major_issues", [])
            minor_issues = final_assessment.get("minor_issues", [])

            if major_issues:
                self.doc.add_heading('重点关注事项', level=2)
                for issue in major_issues:
                    self.doc.add_paragraph(f"• {issue}", style='List Bullet')

            if minor_issues:
                self.doc.add_heading('一般关注事项', level=2)
                for issue in minor_issues:
                    self.doc.add_paragraph(f"• {issue}", style='List Bullet')

        # 下一步操作建议
        if next_action:
            self.doc.add_heading('下一步操作建议', level=2)

            action_type = next_action.get("action_type", "标准流程")
            priority = next_action.get("priority", "中")
            action_details = next_action.get("action_details", [])

            self.doc.add_paragraph(f"建议操作类型：{action_type}")
            self.doc.add_paragraph(f"优先级：{priority}")

            if action_details:
                self.doc.add_paragraph("具体操作建议：")
                for detail in action_details:
                    self.doc.add_paragraph(f"{detail}", style='List Bullet')

        self.doc.add_paragraph()
    
    def _add_recommendations(self, audit_data: Dict[str, Any]):
        """添加建议和后续行动"""
        self.doc.add_heading('七、建议和后续行动', level=1)
        
        next_action = audit_data.get("next_action")
        if not next_action:
            self.doc.add_paragraph("后续行动建议缺失")
            return
        
        action_type = next_action.get("action_type", "未知")
        priority = next_action.get("priority", "未知")
        action_details = next_action.get("action_details", [])
        follow_up = next_action.get("follow_up_required", False)
        
        self.doc.add_paragraph(f"建议行动：{action_type}")
        self.doc.add_paragraph(f"优先级：{priority}")
        self.doc.add_paragraph(f"需要跟进：{'是' if follow_up else '否'}")
        
        if action_details:
            self.doc.add_paragraph("\n具体行动：")
            for i, detail in enumerate(action_details, 1):
                self.doc.add_paragraph(f"{i}. {detail}")
        
        self.doc.add_paragraph()
    
    def _add_footer(self):
        """添加文档尾部"""
        self.doc.add_paragraph()
        self.doc.add_paragraph("—" * 50)
        
        footer_para = self.doc.add_paragraph(
            f"本报告由系统自动生成\n"
            f"生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}"
        )
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
