"""
审核报告生成器
从JSON结果反向生成符合业务规范的审核报告
"""

import json
import logging
from typing import Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class AuditReportGenerator:
    """审核报告生成器 - 从JSON生成审单规则格式的报告"""
    
    def __init__(self):
        pass
    
    def generate_audit_report(self, audit_result: Dict[str, Any]) -> str:
        """生成审核报告"""
        try:
            report = self._build_report_header(audit_result)
            report += self._build_basic_info_section(audit_result)
            report += self._build_risk_assessment_section(audit_result)
            report += self._build_recommendations_section(audit_result)
            
            return report
            
        except Exception as e:
            logger.error(f"生成审核报告失败: {e}")
            return f"报告生成失败: {e}"
    
    def _build_report_header(self, audit_result: Dict[str, Any]) -> str:
        """构建报告头部"""
        applicant_dir = audit_result.get("applicant_dir", "未知申请人")
        audit_date = audit_result.get("audit_date", datetime.now().isoformat())
        
        return f"""
银行经办用户智能审核报告
=====================================

申请人目录: {applicant_dir}
审核时间: {audit_date}
审核系统: 邮政银行代理金融部资格审核系统

"""
    
    def _build_basic_info_section(self, audit_result: Dict[str, Any]) -> str:
        """构建基本信息核验部分"""
        basic_info = audit_result.get("rule_based_analysis", {}).get("basic_info_verification", {})

        report = """
一、基本信息核验（硬性审核，自动化为主）
==========================================

"""

        report += "（一）材料有效性\n"

        # 材料完整性
        completeness = basic_info.get("material_validity", {}).get("completeness", {})
        report += "\n1. 材料完整性\n"
        
        for item, data in completeness.items():
            status = data.get("status", "未检查")
            details = data.get("details", "无详细信息")
            item_name = self._get_material_name(item)
            report += f"   {item_name}: {status} - {details}\n"
        
        # 材料清晰度
        clarity = basic_info.get("material_validity", {}).get("clarity", {})
        report += f"\n2. 材料清晰度\n"
        report += f"   关键信息清晰度: {clarity.get('status', '未检查')} - {clarity.get('details', '无详细信息')}\n"
        
        # 申请表
        app_form = basic_info.get("material_validity", {}).get("application_form", {})
        report += f"\n3. 申请表\n"

        format_integrity = app_form.get('format_integrity', {})
        report += f"   版式完整性: {format_integrity.get('status', '未检查')} - {format_integrity.get('details', '无详细信息')}\n"

        # 添加详细的字段检查信息
        field_check = format_integrity.get('field_check', {})
        if field_check:
            report += f"   字段检查: {field_check.get('required_fields_count', '未知')}\n"

            missing_fields = field_check.get('missing_fields', [])
            if missing_fields:
                report += f"   缺失字段: {', '.join(missing_fields)}\n"

            extra_fields = field_check.get('extra_fields', [])
            if extra_fields:
                report += f"   多余字段: {', '.join(extra_fields)}\n"

            validation_details = field_check.get('field_validation_details', '')
            if validation_details:
                report += f"   字段验证详情: {validation_details}\n"

        report += f"   信息完整性: {app_form.get('information_completeness', {}).get('status', '未检查')} - {app_form.get('information_completeness', {}).get('details', '无详细信息')}\n"
        
        # 征信材料
        credit_materials = basic_info.get("material_validity", {}).get("credit_materials", {})
        report += f"\n4. 征信材料\n"
        credit_date = credit_materials.get("date_validity", {}).get("credit_date", "未提取")
        report += f"   征信报告日期: {credit_date}\n"
        report += f"   日期有效性: {credit_materials.get('date_validity', {}).get('status', '未检查')} - {credit_materials.get('date_validity', {}).get('details', '无详细信息')}\n"
        
        # 信息一致性
        consistency = basic_info.get("information_consistency", {})
        report += f"\n（二）信息一致性\n"
        report += f"\n1. 身份信息核验\n"

        identity_verification = consistency.get('identity_verification', {})
        report += f"   总体状态: {identity_verification.get('status', '未检查')} - {identity_verification.get('details', '无详细信息')}\n"

        # 详细的文件对比结果
        doc_comparison = identity_verification.get("document_comparison", {})
        if doc_comparison:
            report += f"\n   文件对比详情:\n"

            comparisons = [
                ("申请表 vs 身份证", "application_form_vs_id_card"),
                ("申请表 vs 征信报告", "application_form_vs_credit_report"),
                ("申请表 vs 学历材料", "application_form_vs_education_materials"),
                ("申请表 vs 人力截图", "application_form_vs_hr_screenshot"),
                ("文件交叉验证", "cross_document_consistency")
            ]

            for desc, key in comparisons:
                comp_result = doc_comparison.get(key, {})
                if comp_result:
                    status = comp_result.get('status', '未检查')
                    details = comp_result.get('details', '无详细信息')
                    report += f"   • {desc}: {status} - {details}\n"


        
        report += f"\n2. 关键审核项：\n"
        key_items = consistency.get("key_audit_items", {})
        report += f"   学历: {key_items.get('education', {}).get('status', '未检查')} - {key_items.get('education', {}).get('details', '无详细信息')}\n"
        report += f"   工作经验: {key_items.get('work_experience', {}).get('status', '未检查')} - {key_items.get('work_experience', {}).get('details', '无详细信息')}\n"
        report += f"   负债情况: {key_items.get('debt_situation', {}).get('status', '未检查')} - {key_items.get('debt_situation', {}).get('details', '无详细信息')}\n"
        
        # 学历证明
        education = basic_info.get("education_verification", {})
        report += f"\n（三）学历证明\n"
        report += f"   {education.get('status', '未检查')} - {education.get('details', '无详细信息')}\n"
        
        return report
    
    def _build_risk_assessment_section(self, audit_result: Dict[str, Any]) -> str:
        """构建风险评估部分"""
        risk_assessment = audit_result.get("rule_based_analysis", {}).get("risk_assessment", {})

        report = """

二、风险评估（软性审核，自动化为辅）
====================================

"""

        report += "（一）主要风险点\n"

        main_risks = risk_assessment.get("main_risk_points", [])
        if main_risks:
            for risk in main_risks:
                report += f"\n   • {risk.get('description', '未知风险')}: {risk.get('details', '无详细信息')}\n"
        else:
            report += "\n   未发现主要风险点\n"
        
        # 征信风险表格
        credit_risk = risk_assessment.get("credit_risk", {})
        report += f"\n（二）征信风险\n"
        report += f"仅客观记录负债金额、贷款频次等信息，不进行金额是否过大、贷款是否频繁等主观判断。\n\n"
        report += f"{'核查类型':<15} {'结果':<10} {'明细数据'}\n"
        report += f"{'-'*60}\n"
        
        credit_items = [
            ("信用卡情况", credit_risk.get("credit_card_situation", {})),
            ("负债情况", credit_risk.get("debt_situation", {})),
            ("非银机构贷款", credit_risk.get("non_bank_loans", {})),
            ("经营性贷款", credit_risk.get("business_loans", {})),
            ("担保情况", credit_risk.get("guarantee_situation", {})),
            ("征信禁入/瑕疵", credit_risk.get("credit_restrictions", {})),
            ("异常信息", credit_risk.get("abnormal_info", {}))
        ]
        
        for item_name, item_data in credit_items:
            result = item_data.get("result", "未检查")
            details = item_data.get("details", "无详细信息")
            report += f"{item_name:<15} {result:<10} {details}\n"
        
        # 合规风险
        compliance_risk = risk_assessment.get("compliance_risk", {})
        report += f"\n（三）合规风险\n"
        
        execution_cases = compliance_risk.get("execution_info_cases", [])
        if execution_cases:
            report += f"\n1. 执行信息公开网相关案例：\n"
            for case in execution_cases:
                report += f"   • {case.get('case', '未知案例')}: {case.get('details', '无详细信息')}\n"
        else:
            report += f"\n1. 执行信息公开网查询：未发现相关案例\n"
        
        criminal_check = compliance_risk.get("criminal_record_check", {})
        report += f"\n2. 违法犯罪记录检查：{criminal_check.get('result', '未检查')} - {criminal_check.get('details', '无详细信息')}\n"
        
        return report
    
    def _build_recommendations_section(self, audit_result: Dict[str, Any]) -> str:
        """构建建议部分"""
        system_judgment = audit_result.get("rule_based_analysis", {}).get("system_judgment", {})
        next_steps = audit_result.get("rule_based_analysis", {}).get("next_steps_recommendation", [])
        
        report = f"""

三、系统判断（内部参考，不展示给经办）
====================================

自动决策: {system_judgment.get('auto_decision', '未判断')}
决策理由: {system_judgment.get('decision_reason', '无理由')}
置信度: {system_judgment.get('confidence_level', '未知')}

四、下一步操作建议
==================

"""
        
        if next_steps:
            for i, step in enumerate(next_steps, 1):
                report += f"{i}. {step}\n"
        else:
            report += "无特殊操作建议\n"
        
        report += f"\n\n报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        report += f"=====================================\n"
        
        return report
    
    def _get_material_name(self, item_key: str) -> str:
        """获取材料名称"""
        name_mapping = {
            "identity_card": "身份证复印件",
            "education_materials": "学历、学位证书及验证材料",
            "hr_system_screenshot": "邮政人力资源管理系统截图",
            "credit_report": "个人信用报告",
            "execution_info_screenshot": "执行信息公开网查询截图"
        }
        return name_mapping.get(item_key, item_key)
    
    def save_report_to_file(self, report: str, output_path: str) -> bool:
        """保存报告到文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"审核报告已保存到: {output_path}")
            return True
        except Exception as e:
            logger.error(f"保存报告失败: {e}")
            return False


def generate_audit_report_from_json(json_file_path: str, output_path: str = None) -> str:
    """从JSON文件生成审核报告"""
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            audit_result = json.load(f)
        
        generator = AuditReportGenerator()
        report = generator.generate_audit_report(audit_result)
        
        if output_path:
            generator.save_report_to_file(report, output_path)
        
        return report
        
    except Exception as e:
        logger.error(f"从JSON生成报告失败: {e}")
        return f"报告生成失败: {e}"


# 导入时间戳工具
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.timestamp_utils import (
    extract_timestamp_from_filename,
    parse_applicant_name_from_filename,
    generate_audit_report_filename
)


if __name__ == "__main__":
    # 命令行工具
    if len(sys.argv) > 1:
        json_path = sys.argv[1]

        if len(sys.argv) > 2:
            output_path = sys.argv[2]
        else:
            # 自动生成输出文件名，保持时间戳一致
            base_name = os.path.basename(json_path)
            timestamp = extract_timestamp_from_filename(base_name)
            applicant_name = parse_applicant_name_from_filename(base_name)

            if timestamp:
                output_filename = generate_audit_report_filename(applicant_name, timestamp)
            else:
                output_filename = generate_audit_report_filename(applicant_name)

            output_path = f"results/{output_filename}"

        report = generate_audit_report_from_json(json_path, output_path)
        print(f"审核报告已生成: {output_path}")
        print("\n" + "="*50)
        print(report[:500] + "..." if len(report) > 500 else report)
    else:
        print("用法: python audit_report_generator.py <json_file_path> [output_path]")
        print("示例: python audit_report_generator.py results/申请人_audit_result_20250728_175233.json")
        print("     自动生成: results/申请人_audit_report_20250728_175233.txt")
