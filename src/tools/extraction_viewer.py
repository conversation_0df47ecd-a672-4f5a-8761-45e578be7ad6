"""
文档提取结果查看器
用于查看缓存的文档提取结果
"""

import json
import os
import hashlib
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ExtractionViewer:
    """文档提取结果查看器"""
    
    def __init__(self, cache_dir: str = "cache/extractions"):
        self.cache_dir = Path(cache_dir)
        
    def list_cached_extractions(self) -> List[Dict[str, Any]]:
        """列出所有缓存的提取结果"""
        extractions = []
        
        if not self.cache_dir.exists():
            logger.warning(f"缓存目录不存在: {self.cache_dir}")
            return extractions
            
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                extractions.append({
                    "cache_file": cache_file.name,
                    "source_file": data.get("source_file", "未知文件"),
                    "extraction_method": data.get("extraction_method", "未知方法"),
                    "content_length": data.get("content_length", 0),
                    "pages_processed": data.get("pages_processed", 0),
                    "line_count": data.get("line_count", 0),
                    "note": data.get("note", "")
                })
                
            except Exception as e:
                logger.error(f"读取缓存文件失败 {cache_file}: {e}")
                
        return extractions
    
    def get_extraction_by_file(self, source_file: str) -> Optional[Dict[str, Any]]:
        """根据源文件名获取提取结果"""
        # 先尝试直接匹配文件名
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    cached_source = data.get("source_file", "")
                    # 支持完整路径匹配或文件名匹配
                    if cached_source == source_file or os.path.basename(cached_source) == source_file:
                        return data
            except Exception as e:
                logger.error(f"读取缓存文件失败 {cache_file}: {e}")

        return None
    
    def get_extraction_by_cache_key(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """根据缓存键获取提取结果"""
        cache_file = self.cache_dir / f"{cache_key}.json"
        
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"读取缓存文件失败 {cache_file}: {e}")
                
        return None
    
    def view_extraction_content(self, source_file: str, max_lines: int = 50) -> str:
        """查看提取内容（限制行数）"""
        extraction = self.get_extraction_by_file(source_file)
        
        if not extraction:
            return f"未找到文件 {source_file} 的提取结果"
            
        raw_content = extraction.get("raw_content", "")
        lines = raw_content.split('\n')
        
        if len(lines) <= max_lines:
            return raw_content
        else:
            truncated_content = '\n'.join(lines[:max_lines])
            return f"{truncated_content}\n\n... (内容已截断，共{len(lines)}行，显示前{max_lines}行)"
    
    def search_in_extractions(self, keyword: str) -> List[Dict[str, Any]]:
        """在所有提取结果中搜索关键词"""
        results = []
        
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                raw_content = data.get("raw_content", "")
                if keyword.lower() in raw_content.lower():
                    # 找到包含关键词的行
                    lines = raw_content.split('\n')
                    matching_lines = [
                        (i+1, line) for i, line in enumerate(lines)
                        if keyword.lower() in line.lower()
                    ]
                    
                    results.append({
                        "source_file": data.get("source_file", "未知文件"),
                        "cache_file": cache_file.name,
                        "matches": len(matching_lines),
                        "matching_lines": matching_lines[:5]  # 只显示前5个匹配
                    })
                    
            except Exception as e:
                logger.error(f"搜索缓存文件失败 {cache_file}: {e}")
                
        return results
    
    def get_applicant_extractions(self, applicant_dir: str) -> List[Dict[str, Any]]:
        """获取特定申请人的所有提取结果"""
        extractions = []
        applicant_name = os.path.basename(applicant_dir)
        
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                source_file = data.get("source_file", "")
                if applicant_name in source_file or applicant_dir in source_file:
                    extractions.append({
                        "cache_file": cache_file.name,
                        "source_file": source_file,
                        "extraction_method": data.get("extraction_method", "未知方法"),
                        "content_length": data.get("content_length", 0),
                        "pages_processed": data.get("pages_processed", 0),
                        "content_preview": data.get("raw_content", "")[:200] + "..." if len(data.get("raw_content", "")) > 200 else data.get("raw_content", "")
                    })
                    
            except Exception as e:
                logger.error(f"读取缓存文件失败 {cache_file}: {e}")
                
        return extractions
    
    def _get_cache_key(self, file_path: str) -> str:
        """生成缓存键"""
        return hashlib.md5(file_path.encode('utf-8')).hexdigest()
    
    def print_extraction_summary(self):
        """打印提取结果摘要"""
        extractions = self.list_cached_extractions()
        
        if not extractions:
            print("没有找到缓存的提取结果")
            return
            
        print(f"\n=== 缓存的文档提取结果摘要 ===")
        print(f"总计: {len(extractions)} 个文件")
        print(f"缓存目录: {self.cache_dir}")
        print()
        
        for i, ext in enumerate(extractions, 1):
            print(f"{i:2d}. {ext['source_file']}")
            print(f"    方法: {ext['extraction_method']}")
            print(f"    长度: {ext['content_length']} 字符")
            print(f"    页数: {ext['pages_processed']} 页")
            print(f"    行数: {ext['line_count']} 行")
            print(f"    缓存: {ext['cache_file']}")
            print()


def main():
    """命令行工具主函数"""
    import sys
    import argparse
    
    parser = argparse.ArgumentParser(description="文档提取结果查看器")
    parser.add_argument("--list", action="store_true", help="列出所有缓存的提取结果")
    parser.add_argument("--view", type=str, help="查看指定文件的提取内容")
    parser.add_argument("--search", type=str, help="在所有提取结果中搜索关键词")
    parser.add_argument("--applicant", type=str, help="查看指定申请人的所有提取结果")
    parser.add_argument("--cache-key", type=str, help="根据缓存键查看提取结果")
    parser.add_argument("--max-lines", type=int, default=50, help="查看内容时的最大行数")
    
    args = parser.parse_args()
    
    viewer = ExtractionViewer()
    
    if args.list:
        viewer.print_extraction_summary()
        
    elif args.view:
        content = viewer.view_extraction_content(args.view, args.max_lines)
        print(f"\n=== {args.view} 的提取内容 ===")
        print(content)
        
    elif args.search:
        results = viewer.search_in_extractions(args.search)
        print(f"\n=== 搜索结果: '{args.search}' ===")
        if results:
            for result in results:
                print(f"\n文件: {result['source_file']}")
                print(f"匹配: {result['matches']} 处")
                for line_num, line in result['matching_lines']:
                    print(f"  第{line_num}行: {line.strip()}")
        else:
            print("未找到匹配结果")
            
    elif args.applicant:
        extractions = viewer.get_applicant_extractions(args.applicant)
        print(f"\n=== 申请人 {args.applicant} 的提取结果 ===")
        if extractions:
            for ext in extractions:
                print(f"\n文件: {ext['source_file']}")
                print(f"方法: {ext['extraction_method']}")
                print(f"长度: {ext['content_length']} 字符")
                print(f"页数: {ext['pages_processed']} 页")
                print(f"预览: {ext['content_preview']}")
        else:
            print("未找到该申请人的提取结果")
            
    elif args.cache_key:
        extraction = viewer.get_extraction_by_cache_key(args.cache_key)
        if extraction:
            print(f"\n=== 缓存键 {args.cache_key} 的提取结果 ===")
            print(f"源文件: {extraction.get('source_file', '未知')}")
            print(f"方法: {extraction.get('extraction_method', '未知')}")
            print(f"长度: {extraction.get('content_length', 0)} 字符")
            print(f"页数: {extraction.get('pages_processed', 0)} 页")
            print(f"\n内容:")
            content = extraction.get('raw_content', '')
            lines = content.split('\n')
            if len(lines) <= args.max_lines:
                print(content)
            else:
                print('\n'.join(lines[:args.max_lines]))
                print(f"\n... (内容已截断，共{len(lines)}行，显示前{args.max_lines}行)")
        else:
            print(f"未找到缓存键 {args.cache_key} 的提取结果")
            
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
