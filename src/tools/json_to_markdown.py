"""
JSON到Markdown转换器
将审核结果JSON转换为Markdown格式报告
"""

import os
import json
import logging
from typing import Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class JSONToMarkdownConverter:
    """JSON到Markdown转换器"""
    
    def __init__(self):
        pass
    
    def convert(self, audit_data: Dict[str, Any], output_path: str):
        """将审核结果转换为Markdown报告"""
        try:
            # 生成Markdown内容
            markdown_content = self._generate_markdown(audit_data)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logger.info(f"Markdown报告已生成: {output_path}")
            
        except Exception as e:
            logger.error(f"生成Markdown报告失败: {e}")
            raise
    
    def _generate_markdown(self, audit_data: Dict[str, Any]) -> str:
        """生成Markdown内容"""
        content = []
        
        # 标题和基本信息
        content.append("# 代理营业机构负责人任职资格审核报告")
        content.append("")
        
        # 基本信息
        content.extend(self._add_basic_info(audit_data))
        
        # 文档概要
        content.extend(self._add_document_summary(audit_data))
        
        # 基本信息核验
        content.extend(self._add_basic_verification(audit_data))
        
        # 风险评估
        content.extend(self._add_risk_assessment(audit_data))
        
        # 审核建议
        content.extend(self._add_final_assessment(audit_data))
        
        return "\n".join(content)
    
    def _add_basic_info(self, audit_data: Dict[str, Any]) -> list:
        """添加基本信息"""
        content = []
        content.append("## 基本信息")
        content.append("")
        
        # 审核信息
        audit_id = audit_data.get("audit_id", "N/A")
        audit_date = audit_data.get("audit_date", "N/A")
        applicant_dir = audit_data.get("applicant_dir", "N/A")
        
        content.append(f"- **审核ID**: {audit_id}")
        content.append(f"- **审核时间**: {audit_date}")
        content.append(f"- **申请人目录**: {applicant_dir}")
        content.append("")
        
        # 从提取数据中获取申请人信息
        extracted_data = audit_data.get("extracted_data", {})
        if "1" in extracted_data:  # 申请表
            app_content = extracted_data["1"].get("content", {})
            personal_info = app_content.get("personal_info", {})
            
            content.append("### 申请人信息")
            content.append("")
            content.append(f"- **姓名**: {personal_info.get('name', '未提供')}")
            content.append(f"- **身份证号**: {personal_info.get('id_number', '未提供')}")
            content.append(f"- **性别**: {personal_info.get('gender', '未提供')}")
            content.append(f"- **出生日期**: {personal_info.get('birth_date', '未提供')}")
            content.append(f"- **联系电话**: {personal_info.get('phone', '未提供')}")
            content.append("")
        
        return content
    
    def _add_document_summary(self, audit_data: Dict[str, Any]) -> list:
        """添加文档概要"""
        content = []
        content.append("## 文档概要")
        content.append("")
        
        doc_summary = audit_data.get("document_summary", {})
        
        content.append(f"- **总文档数**: {doc_summary.get('total_documents', 0)}")
        
        # 标准文档
        standard_docs = doc_summary.get("standard_documents", {})
        content.append(f"- **标准文档**: {standard_docs.get('count', 0)} 个")
        if standard_docs.get('types'):
            for doc_type in standard_docs['types']:
                content.append(f"  - {doc_type}")
        
        # 补充材料
        supplementary_docs = doc_summary.get("supplementary_materials", {})
        content.append(f"- **补充材料**: {supplementary_docs.get('count', 0)} 个")
        if supplementary_docs.get('types'):
            for doc_type in supplementary_docs['types']:
                content.append(f"  - {doc_type}")
        
        # 缺失文档
        missing = doc_summary.get("missing_required", [])
        if missing:
            content.append(f"- **缺失文档**: {', '.join(missing)}")
        
        content.append("")
        return content
    
    def _add_basic_verification(self, audit_data: Dict[str, Any]) -> list:
        """添加基本信息核验"""
        content = []
        content.append("## 一、基本信息核验（硬性审核）")
        content.append("")
        
        basic_verification = audit_data.get("basic_verification", {})
        verification_items = basic_verification.get("verification_items", {})
        
        # 验证项目表格
        content.append("| 验证项目 | 结果 | 详细说明 |")
        content.append("|---------|------|----------|")
        
        for item, result in verification_items.items():
            result_status = result.get("result", "N/A")
            details = result.get("details", "")
            content.append(f"| {item} | {result_status} | {details} |")
        
        content.append("")
        
        # 总体结果
        overall_result = basic_verification.get("overall_result", {})
        content.append(f"**总体验证结果**: {overall_result.get('result', 'N/A')}")
        content.append("")
        content.append(f"**说明**: {overall_result.get('details', '')}")
        content.append("")
        
        return content
    
    def _add_risk_assessment(self, audit_data: Dict[str, Any]) -> list:
        """添加风险评估"""
        content = []
        content.append("## 二、风险评估（软性审核，自动化为辅）")
        content.append("")
        
        risk_assessment = audit_data.get("risk_assessment", {})
        assessment_items = risk_assessment.get("assessment_items", {})
        
        # 风险评估表格
        content.append("| 核查类型 | 结果 | 明细数据 |")
        content.append("|---------|------|----------|")
        
        for item, result in assessment_items.items():
            result_status = result.get("result", "N/A")
            evidence = result.get("evidence", [])
            details = "; ".join(evidence) if evidence else "无相关记录"
            content.append(f"| {item} | {result_status} | {details} |")
        
        content.append("")
        
        # 主要风险点
        overall_risk = risk_assessment.get("overall_risk", {})
        content.append("### （一）主要风险点")
        content.append("")
        
        risk_items = overall_risk.get("risk_items", [])
        if risk_items:
            for item in risk_items:
                content.append(f"- {item}")
        else:
            content.append("- 未发现重大风险因素")
        
        content.append("")
        
        # 征信风险说明
        content.append("### （二）征信风险")
        content.append("")
        content.append("仅客观记录负债金额、贷款频次等信息，不进行金额是否过大、贷款是否频繁等主观判断。")
        content.append("")
        
        # 合规风险
        content.append("### （三）合规风险")
        content.append("")
        
        # 查找执行风险相关信息
        execution_info = assessment_items.get("异常信息", {})
        if execution_info.get("evidence"):
            for evidence in execution_info["evidence"]:
                content.append(f"- {evidence}")
        else:
            content.append("- 执行网查询结果清白")
        
        content.append("")
        return content
    
    def _add_final_assessment(self, audit_data: Dict[str, Any]) -> list:
        """添加最终评估"""
        content = []
        content.append("## 三、审核建议")
        content.append("")
        
        final_assessment = audit_data.get("final_assessment", {})
        next_action = audit_data.get("next_action", {})
        
        # 重点关注事项
        major_issues = final_assessment.get("major_issues", [])
        if major_issues:
            content.append("### 重点关注事项")
            content.append("")
            for issue in major_issues:
                content.append(f"- {issue}")
            content.append("")
        
        # 一般关注事项
        minor_issues = final_assessment.get("minor_issues", [])
        if minor_issues:
            content.append("### 一般关注事项")
            content.append("")
            for issue in minor_issues:
                content.append(f"- {issue}")
            content.append("")
        
        # 下一步操作建议
        if next_action:
            content.append("### 下一步操作建议")
            content.append("")
            
            action_type = next_action.get("action_type", "标准流程")
            priority = next_action.get("priority", "中")
            
            content.append(f"- **建议操作类型**: {action_type}")
            content.append(f"- **优先级**: {priority}")
            content.append("")
            
            action_details = next_action.get("action_details", [])
            if action_details:
                content.append("**具体操作建议**:")
                content.append("")
                for detail in action_details:
                    content.append(f"- {detail}")
                content.append("")
        
        # 报告生成信息
        content.append("---")
        content.append("")
        content.append(f"*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")
        content.append("")
        content.append("*本报告由智能审核系统自动生成*")
        
        return content
