# 纯净架构使用指南

## 概述

系统已成功实现两步分离架构：
1. **文档提取**：VLM提取文档内容并缓存
2. **审核报告生成**：基于缓存内容生成纯净的审核报告

## 架构优势

✅ **数据分离**：86.6% 的原始数据已分离到缓存，审核报告更加纯净  
✅ **性能优化**：提取结果可复用，避免重复VLM调用  
✅ **存储效率**：审核结果文件从几十KB减少到9.2KB  
✅ **业务对齐**：审核报告格式完全符合业务要求  
✅ **独立管理**：提取结果和审核报告可独立查看和管理  

## 使用流程

### 1. 运行完整审核
```bash
# 基本审核
python audit_cli.py audit "data0724/20160212111(河北石家庄）" --output-dir results --verbose

# 带缓存控制的审核
python audit_cli.py audit "data0724/20160212111(河北石家庄）" --output-dir results --clear-cache-before-run
```

### 2. 查看提取结果
```bash
# 列出所有缓存的提取结果
python src/tools/extraction_viewer.py --list

# 查看特定文件的提取内容
python src/tools/extraction_viewer.py --view "1张导-申请表.pdf" --max-lines 20

# 搜索关键词
python src/tools/extraction_viewer.py --search "张导"

# 查看特定申请人的所有提取结果
python src/tools/extraction_viewer.py --applicant "20160212111(河北石家庄）"
```

### 3. 生成格式化报告
```bash
# 从JSON生成业务格式报告
python src/tools/audit_report_generator.py "results/20160212111(河北石家庄）_audit_result.json" "results/audit_report.txt"
```

### 4. 缓存管理
```bash
# 查看缓存状态
python audit_cli.py cache --status

# 清理所有缓存
python audit_cli.py cache --clear

# 只清理图片缓存
python audit_cli.py cache --clear-images

# 只清理提取结果缓存
python audit_cli.py cache --clear-extractions
```

## 文件结构

### 审核结果文件 (9.2KB)
```
results/20160212111(河北石家庄）_audit_result.json
├── audit_id: 审核ID
├── document_summary: 文档摘要
├── rule_based_analysis: 审核分析结果
└── extraction_cache_info: 缓存信息引用
```

### 提取结果缓存 (59.3KB)
```
cache/extractions/
├── 24ec0002652dfff2a3d001a0308c705d.json  # 1张导-申请表.pdf
├── 645e6e82fe4ff54a1c6a618a1f40eb47.json  # 2张导-身份证件.pdf
├── 9562adcd2ff58df87458ae4b25fc72d6.json  # 3张导-征信.pdf
├── aa0b94b1e2ad02ea2cf04a47ddfa6dc1.json  # 4张导-学历.pdf
├── 67f0534f6d4ac55f91bae7a7e9f71f7b.json  # 5张导-人力截屏.pdf
├── a96212880f9d65cd0a5fa4cf118fefba.json  # 6张导-被执行人查询.pdf
└── 8a4f95edd0c3550004598b8671e84acc.json  # 7张导-岗位证书.pdf
```

### 业务格式报告 (3.9KB)
```
results/20160212111(河北石家庄）_audit_report.txt
├── 一、基本信息核验（硬性审核，自动化为主）
├── 二、风险评估（软性审核，自动化为辅）
├── 三、系统判断（内部参考，不展示给经办）
└── 四、下一步操作建议
```

## 配置选项

### LLM配置
- **默认模型**：Qwen/Qwen2.5-72B-Instruct-128K (长上下文支持)
- **最大令牌**：4000 (符合API限制)
- **提供商**：硅基智能 (SiliconFlow)

### 缓存控制
```yaml
# config.yaml
CACHE_CONFIG:
  clear_before_run: false          # 运行前是否清空所有缓存
  clear_images_before_run: false   # 运行前是否清空图片缓存
  clear_extractions_before_run: false  # 运行前是否清空提取结果缓存
```

## 性能数据

| 指标 | 数值 |
|------|------|
| 审核结果文件大小 | 9.2KB |
| 提取结果缓存大小 | 59.3KB |
| 业务报告文件大小 | 3.9KB |
| 数据分离比例 | 86.6% |
| 缓存文件数量 | 7个 |
| 有效缓存率 | 100% |

## 故障排除

### 1. 审核结果包含raw_content
```bash
# 检查架构完整性
python test_clean_architecture.py
```

### 2. 缓存文件损坏
```bash
# 清理并重新生成缓存
python audit_cli.py cache --clear
python audit_cli.py audit "申请人目录"
```

### 3. 提取结果查看器无法找到文件
```bash
# 检查文件名匹配
python src/tools/extraction_viewer.py --list
```

### 4. LLM调用失败
```bash
# 检查API配置
python audit_cli.py test --config basic_config.yaml
```

## 开发者信息

- **架构模式**：两步分离 (文档提取 + 审核报告)
- **缓存策略**：MD5哈希键，文件级缓存
- **数据格式**：JSON (结构化) + TXT (业务格式)
- **API集成**：Qwen VLM + Qwen LLM
- **并发控制**：多级线程池，API限流保护

## 更新日志

### v2.0 - 纯净架构
- ✅ 实现文档提取与审核报告完全分离
- ✅ 移除审核结果中的raw_content冗余数据
- ✅ 新增提取结果查看器工具
- ✅ 优化LLM配置，使用Qwen长上下文模型
- ✅ 重新设计审核报告格式，符合业务要求
- ✅ 增强缓存控制功能
- ✅ 提供完整的命令行工具集

### 下一步计划
- 🔄 支持批量审核多个申请人
- 📊 添加审核统计和分析功能
- 🔍 增强搜索和过滤功能
- 📝 支持自定义报告模板
- 🔗 集成外部系统API
