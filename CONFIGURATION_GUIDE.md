# 智能审核系统配置指南

## 概述

智能审核系统现在支持灵活的配置管理，用户可以通过多种方式自定义系统参数：

- **命令行参数**: 直接在命令行中指定参数
- **配置文件**: 使用YAML或JSON格式的配置文件
- **环境变量**: 通过环境变量设置参数
- **配置模板**: 使用预定义的配置模板

## 快速开始

### 1. 生成配置文件

```bash
# 生成基础配置文件
python audit_cli.py config generate --template basic --output my_config.yaml

# 生成高级配置文件
python audit_cli.py config generate --template advanced --output advanced_config.yaml

# 生成开发配置文件
python audit_cli.py config generate --template development --output dev_config.yaml
```

### 2. 使用配置文件运行测试

```bash
# 使用配置文件
python audit_cli.py test --config my_config.yaml

# 使用配置文件并覆盖部分参数
python audit_cli.py test --config my_config.yaml --data-dir custom_data --page-workers 1
```

### 3. 直接使用命令行参数

```bash
# 自定义数据路径和线程数
python audit_cli.py test --data-dir data0724 --page-workers 2 --doc-workers 4 --max-test-files 2

# 禁用缓存并设置API限制
python audit_cli.py test --disable-cache --api-rate-limit 20 --max-retries 3
```

## 可配置参数

### 数据路径配置

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--data-dir` | 测试数据目录路径 | `data0724` | `--data-dir /path/to/data` |
| `--cache-dir` | 缓存目录路径 | `cache` | `--cache-dir /tmp/cache` |
| `--output-dir` | 输出目录路径 | `results` | `--output-dir /path/to/output` |

### 并发配置

| 参数 | 说明 | 默认值 | 范围 |
|------|------|--------|------|
| `--page-workers` | 页面级并发线程数 | `2` | 1-8 |
| `--doc-workers` | 文档级并发线程数 | `3` | 1-10 |
| `--user-workers` | 用户级并发线程数 | `2` | 1-5 |

### API配置

| 参数 | 说明 | 默认值 | 范围 |
|------|------|--------|------|
| `--api-rate-limit` | API调用频率限制（次/分钟） | `30` | 10-100 |
| `--max-retries` | 最大重试次数 | `3` | 1-10 |

### 测试配置

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--max-test-files` | 最大测试文件数 | `3` | `--max-test-files 1` |
| `--with-llm` | 启用LLM测试 | `false` | `--with-llm` |
| `--with-vlm` | 启用VLM测试 | `false` | `--with-vlm` |
| `--disable-cache` | 禁用缓存 | `false` | `--disable-cache` |

## 配置模板

### 基础模板 (basic)
适用于一般用户，保守的并发设置：
```yaml
data_dir: data0724
cache_dir: cache
output_dir: results
page_workers: 2
doc_workers: 3
user_workers: 2
enable_cache: true
api_rate_limit: 30
max_retries: 3
```

### 高级模板 (advanced)
适用于高级用户，更高的并发和性能：
```yaml
data_dir: data0724
cache_dir: cache
output_dir: results
page_workers: 4
doc_workers: 6
user_workers: 3
enable_cache: true
api_rate_limit: 60
max_retries: 5
batch_delay_seconds: 1.0
concurrent_limit: 8
cache_ttl_hours: 48
max_cache_size_mb: 2000
```

### 开发模板 (development)
适用于开发和测试环境：
```yaml
data_dir: test_data
cache_dir: dev_cache
output_dir: dev_results
page_workers: 1
doc_workers: 2
user_workers: 1
enable_cache: true
api_rate_limit: 10
max_retries: 2
enable_vlm_tests: true
enable_llm_tests: false
test_timeout_seconds: 120
max_test_files: 1
```

### 生产模板 (production)
适用于生产环境：
```yaml
data_dir: production_data
cache_dir: production_cache
output_dir: production_results
page_workers: 3
doc_workers: 5
user_workers: 3
enable_cache: true
api_rate_limit: 50
max_retries: 5
batch_delay_seconds: 2.0
concurrent_limit: 10
cache_ttl_hours: 72
max_cache_size_mb: 5000
cleanup_temp_files: true
max_concurrent_audits: 5
```

## 命令行工具使用

### 审核功能

```bash
# 审核单个申请人
python audit_cli.py audit data0724/20080766971（山西太原）

# 批量审核
python audit_cli.py batch-audit data0724/ --max-applicants 5

# 使用自定义配置审核
python audit_cli.py audit data0724/20080766971（山西太原） --config advanced_config.yaml
```

### 测试功能

```bash
# 基础测试
python audit_cli.py test

# 完整测试（包含LLM）
python audit_cli.py test --with-llm

# 自定义测试参数
python audit_cli.py test --data-dir custom_data --page-workers 1 --max-test-files 1
```

### 缓存管理

```bash
# 查看缓存状态
python audit_cli.py cache --stats

# 清理7天前的缓存
python audit_cli.py cache --clean 7

# 验证缓存完整性
python audit_cli.py cache --validate

# 清空所有缓存
python audit_cli.py cache --clear-all
```

### 配置管理

```bash
# 生成配置文件
python audit_cli.py config generate --template basic

# 验证当前配置
python audit_cli.py config validate

# 显示当前配置
python audit_cli.py config show
```

## 环境变量

系统支持通过环境变量设置配置：

```bash
# 设置数据目录
export DATA_DIR="/path/to/data"

# 设置API密钥
export DEEPSEEK_API_KEY="your-api-key"
export QWEN_API_KEY="your-qwen-api-key"

# 设置日志级别
export LOG_LEVEL="DEBUG"

# 设置并发参数
export MAX_CONCURRENT_AUDITS="5"
```

## 性能调优建议

### 低配置环境
```bash
python audit_cli.py test --page-workers 1 --doc-workers 2 --user-workers 1 --api-rate-limit 10
```

### 高配置环境
```bash
python audit_cli.py test --page-workers 4 --doc-workers 8 --user-workers 3 --api-rate-limit 60
```

### API限制环境
```bash
python audit_cli.py test --api-rate-limit 20 --max-retries 5 --page-workers 2
```

## 故障排除

### 常见问题

1. **数据目录未找到**
   ```bash
   # 检查可用的数据目录
   ls -la data*/
   
   # 指定正确的数据目录
   python audit_cli.py test --data-dir data0724
   ```

2. **API调用失败**
   ```bash
   # 降低并发和频率
   python audit_cli.py test --page-workers 1 --api-rate-limit 10 --max-retries 5
   ```

3. **缓存问题**
   ```bash
   # 清空缓存重试
   python audit_cli.py cache --clear-all
   python audit_cli.py test --disable-cache
   ```

### 调试模式

```bash
# 启用详细日志
python audit_cli.py test --verbose

# 静默模式
python audit_cli.py test --quiet
```

## 最佳实践

1. **首次使用**: 从基础模板开始，逐步调整参数
2. **性能测试**: 使用小数据集测试最佳参数组合
3. **生产环境**: 使用配置文件而非命令行参数
4. **监控**: 定期检查缓存状态和系统性能
5. **备份**: 保存有效的配置文件供后续使用
