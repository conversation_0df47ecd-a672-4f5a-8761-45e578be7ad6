#!/usr/bin/env python3
"""
测试Qwen JSON Mode功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.llm_client import LLMClient
import json

def test_json_mode():
    """测试JSON Mode功能"""
    print("=== 测试Qwen JSON Mode ===")
    
    # 初始化LLM客户端
    llm_client = LLMClient()
    
    # 简单的JSON测试
    user_message = "请提取以下信息并以JSON格式返回：姓名：张三，年龄：25岁，职业：工程师"
    system_message = "你是一个信息提取专家，请将用户提供的信息提取为JSON格式。"
    
    print(f"用户消息: {user_message}")
    print(f"系统消息: {system_message}")
    print("\n" + "="*50)
    
    try:
        result = llm_client.chat_with_json(
            user_message=user_message,
            system_message=system_message
        )
        
        print("返回结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        if "error" in result:
            print(f"\n❌ JSON解析失败")
            print(f"原始响应: {result.get('raw_response', '')[:500]}...")
        else:
            print(f"\n✅ JSON解析成功")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_json_mode()
