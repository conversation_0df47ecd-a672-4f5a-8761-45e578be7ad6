#!/usr/bin/env python3
"""
测试JSON清理逻辑
"""

import json

def test_json_cleanup():
    """测试JSON清理逻辑"""
    
    # 模拟有问题的响应
    problematic_response = """根据提供的审核规则文档和申请人材料，以下是结构化的审核结果：

```-json
{
    "basic_info_verification": {
        "material_validity": {
            "completeness": {
                "identity_card": {"status": "符合", "details": "提供了身份证复印件"}
            }
        }
    }
}
```"""
    
    print("原始响应:")
    print(problematic_response)
    print("\n" + "="*50)
    
    # 应用改进的清理逻辑
    cleaned_text = problematic_response.strip()

    # 查找JSON代码块
    import re

    # 查找```开头的代码块（可能有各种标记如json、-json等）
    code_block_pattern = r'```[^`\n]*\n(.*?)```'
    match = re.search(code_block_pattern, cleaned_text, re.DOTALL)

    if match:
        # 提取代码块内容
        cleaned_text = match.group(1).strip()
    else:
        # 如果没有找到代码块，尝试查找JSON对象
        json_pattern = r'\{.*\}'
        json_match = re.search(json_pattern, cleaned_text, re.DOTALL)
        if json_match:
            cleaned_text = json_match.group(0).strip()
        else:
            # 如果都没找到，保持原样
            pass
    
    print("清理后的文本:")
    print(cleaned_text)
    print("\n" + "="*50)
    
    try:
        parsed = json.loads(cleaned_text)
        print("✅ JSON解析成功!")
        print(json.dumps(parsed, ensure_ascii=False, indent=2))
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")

if __name__ == "__main__":
    test_json_cleanup()
