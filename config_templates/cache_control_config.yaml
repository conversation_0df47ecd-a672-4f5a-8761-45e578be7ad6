# 缓存控制配置模板
# 用于演示各种缓存控制选项

# 基础配置
data_dir: "data0724"
cache_dir: "cache"
output_dir: "results"

# 并发配置
page_workers: 3
doc_workers: 2
user_workers: 1

# API配置
api_rate_limit: 30
max_retries: 3

# 缓存控制配置
cache_control:
  # 运行前清空所有缓存（慎用，会删除所有缓存数据）
  clear_before_run: false
  
  # 运行前只清空图片缓存（PDF转图片的缓存）
  clear_images_before_run: false
  
  # 运行前只清空提取结果缓存（VLM提取结果的缓存）
  clear_extractions_before_run: false
  
  # 自动清理N天前的旧缓存
  auto_clear_old_cache_days: 7

# 测试配置
execution:
  enable_vlm_tests: true
  enable_llm_tests: false
  test_timeout_seconds: 300
  max_test_files: 3
