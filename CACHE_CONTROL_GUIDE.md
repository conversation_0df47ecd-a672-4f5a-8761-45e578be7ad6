# 缓存控制功能使用指南

## 概述

系统现在支持灵活的缓存控制，可以在运行前自动清理缓存，避免使用过期或错误的缓存数据。

## 缓存类型

系统有两种主要的缓存：

1. **图片缓存** (`cache/images/`)
   - 存储PDF转换为图片的结果
   - 加速重复处理同一PDF文件
   - 通常占用较多磁盘空间

2. **提取结果缓存** (`cache/extractions/`)
   - 存储VLM提取的文本结果
   - 避免重复调用VLM API
   - 包含完整的文档内容和元数据

## 使用方法

### 1. 命令行参数控制

在任何命令中添加缓存控制参数：

```bash
# 运行前清空所有缓存
python audit_cli.py audit "申请人目录" --clear-cache-before-run

# 运行前只清空图片缓存
python audit_cli.py audit "申请人目录" --clear-images-before-run

# 运行前只清空提取结果缓存
python audit_cli.py audit "申请人目录" --clear-extractions-before-run

# 可以组合使用
python audit_cli.py test --clear-images-before-run --clear-extractions-before-run
```

### 2. 配置文件控制

在配置文件中设置缓存控制选项：

```yaml
# config.yaml
cache_control:
  # 运行前清空所有缓存（慎用）
  clear_before_run: false
  
  # 运行前只清空图片缓存
  clear_images_before_run: false
  
  # 运行前只清空提取结果缓存
  clear_extractions_before_run: false
  
  # 自动清理N天前的旧缓存
  auto_clear_old_cache_days: 7
```

然后使用配置文件：

```bash
python audit_cli.py audit "申请人目录" --config your_config.yaml
```

### 3. 手动缓存管理

直接管理缓存：

```bash
# 查看缓存统计
python audit_cli.py cache --stats

# 清空所有缓存
python audit_cli.py cache --clear-all

# 只清空图片缓存
python audit_cli.py cache --clear-images

# 只清空提取结果缓存
python audit_cli.py cache --clear-extractions

# 清理7天前的旧缓存
python audit_cli.py cache --clean 7

# 验证缓存完整性
python audit_cli.py cache --validate
```

## 使用场景

### 场景1: 开发测试
```bash
# 每次测试前清空缓存，确保使用最新代码逻辑
python audit_cli.py test --clear-cache-before-run
```

### 场景2: 生产环境
```bash
# 只清空可能过期的提取结果缓存，保留图片缓存提高性能
python audit_cli.py audit "申请人目录" --clear-extractions-before-run
```

### 场景3: 磁盘空间不足
```bash
# 清空占用空间较大的图片缓存
python audit_cli.py cache --clear-images
```

### 场景4: 批量处理
```bash
# 批量处理前清空旧缓存，避免混淆
python audit_cli.py batch-audit "数据目录" --clear-cache-before-run
```

## 配置模板

系统提供了缓存控制配置模板：

```bash
# 使用缓存控制配置模板
python audit_cli.py test --config config_templates/cache_control_config.yaml
```

## 注意事项

1. **谨慎使用 `--clear-cache-before-run`**
   - 会删除所有缓存，首次运行会较慢
   - 适用于确保完全重新处理的场景

2. **图片缓存 vs 提取结果缓存**
   - 图片缓存相对稳定，除非PDF文件变化
   - 提取结果缓存可能因为提取逻辑变化而需要更新

3. **性能影响**
   - 清空缓存后首次运行会较慢
   - 建议根据实际需求选择性清理

4. **自动清理**
   - 系统会自动清理过期缓存
   - 可通过 `auto_clear_old_cache_days` 配置清理周期

## 示例工作流

### 日常使用
```bash
# 1. 查看当前缓存状态
python audit_cli.py cache --stats

# 2. 正常审核（使用缓存）
python audit_cli.py audit "申请人目录"

# 3. 定期清理旧缓存
python audit_cli.py cache --clean 7
```

### 开发调试
```bash
# 1. 修改代码后清空提取结果缓存
python audit_cli.py cache --clear-extractions

# 2. 测试新逻辑
python audit_cli.py test

# 3. 如果需要完全重新处理
python audit_cli.py test --clear-cache-before-run
```

这样您就可以灵活控制缓存的使用，既能享受缓存带来的性能提升，又能确保数据的准确性！
