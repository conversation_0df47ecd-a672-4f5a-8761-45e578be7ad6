#!/usr/bin/env python3
"""
测试移除手写识别字段后的系统功能
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.intelligent_audit_service import IntelligentAuditService
from src.tools.audit_report_generator import AuditReportGenerator
from src.utils.config_manager import <PERSON><PERSON>g<PERSON><PERSON><PERSON>

def test_handwriting_removal():
    """测试移除手写识别字段后的审核功能"""
    
    print("🧪 测试移除手写识别字段后的系统功能")
    print("=" * 60)
    
    # 1. 初始化服务
    print("1. 初始化审核服务...")
    audit_service = IntelligentAuditService()
    
    # 2. 执行审核
    applicant_dir = "data0724/20160212111(河北石家庄）"
    if not os.path.exists(applicant_dir):
        print(f"❌ 申请人目录不存在: {applicant_dir}")
        return False
    
    print(f"2. 执行审核: {applicant_dir}")
    try:
        audit_result = audit_service.audit_applicant(applicant_dir)
        print("✅ 审核完成")
    except Exception as e:
        print(f"❌ 审核失败: {e}")
        return False
    
    # 3. 检查身份信息一致性部分
    print("3. 检查身份信息一致性结构...")
    identity_verification = audit_result.get("rule_based_analysis", {}).get("basic_info_verification", {}).get("information_consistency", {}).get("identity_verification", {})
    
    print(f"   身份验证状态: {identity_verification.get('status', '未知')}")
    print(f"   详细信息: {identity_verification.get('details', '无')}")
    
    # 检查是否还有手写识别字段
    if "handwriting_recognition_notes" in identity_verification:
        print("❌ 错误：仍然包含handwriting_recognition_notes字段")
        return False
    else:
        print("✅ 正确：已移除handwriting_recognition_notes字段")
    
    # 检查文档对比是否正常
    document_comparison = identity_verification.get("document_comparison", {})
    if document_comparison:
        print("✅ 文档对比功能正常")
        for doc_type, comparison in document_comparison.items():
            print(f"     - {doc_type}: {comparison.get('status', '未知')}")
    else:
        print("⚠️  警告：文档对比信息为空")
    
    # 4. 生成报告测试
    print("4. 测试报告生成...")
    try:
        report_generator = AuditReportGenerator()
        
        # 生成JSON文件名（带时间戳）
        from src.utils.timestamp_utils import generate_audit_result_filename
        audit_id = "test_handwriting_removal"
        json_filename = generate_audit_result_filename("测试申请人", audit_id)
        json_path = f"results/{json_filename}"
        
        # 保存JSON结果
        os.makedirs("results", exist_ok=True)
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(audit_result, f, ensure_ascii=False, indent=2)
        
        # 生成TXT报告
        txt_content = report_generator.generate_audit_report(audit_result)
        
        # 检查TXT报告中是否包含手写识别相关内容
        if "手写签字识别" in txt_content or "签字清晰度" in txt_content or "识别置信度" in txt_content:
            print("❌ 错误：TXT报告仍包含手写识别相关内容")
            return False
        else:
            print("✅ 正确：TXT报告已移除手写识别相关内容")
        
        # 保存TXT报告
        txt_filename = json_filename.replace("_audit_result_", "_audit_report_").replace(".json", ".txt")
        txt_path = f"results/{txt_filename}"
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write(txt_content)
        
        print(f"✅ 报告生成成功:")
        print(f"   JSON: {json_path}")
        print(f"   TXT:  {txt_path}")
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        return False
    
    print("\n🎉 测试完成：手写识别字段已成功移除，系统功能正常！")
    return True

if __name__ == "__main__":
    success = test_handwriting_removal()
    sys.exit(0 if success else 1)
