#!/usr/bin/env python3
"""
简单的Markdown转Word工具
重点处理标题结构，忽略复杂的markdown格式
"""

import argparse
import os
import re
from pathlib import Path
from docx import Document
from docx.shared import Inches
from docx.enum.style import WD_STYLE_TYPE

def create_word_styles(doc):
    """创建Word文档样式"""
    styles = doc.styles
    
    # 确保有标题样式
    heading_styles = []
    for i in range(1, 5):
        style_name = f'Heading {i}'
        if style_name not in [s.name for s in styles]:
            style = styles.add_style(style_name, WD_STYLE_TYPE.PARAGRAPH)
            style.base_style = styles['Normal']
        heading_styles.append(styles[style_name])
    
    return heading_styles

def convert_md_to_word(md_file_path, word_file_path=None):
    """
    将Markdown文件转换为Word文档
    
    Args:
        md_file_path: Markdown文件路径
        word_file_path: 输出Word文件路径，如果为None则自动生成
    """
    
    # 读取Markdown文件
    try:
        with open(md_file_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
    except Exception as e:
        print(f"❌ 读取Markdown文件失败: {e}")
        return False
    
    # 创建Word文档
    doc = Document()
    
    # 创建样式
    heading_styles = create_word_styles(doc)
    
    # 按行处理Markdown内容
    lines = md_content.split('\n')
    current_paragraph = ""
    
    for line in lines:
        line = line.rstrip()
        
        # 检查是否为标题行
        heading_match = re.match(r'^(#{1,4})\s+(.+)', line)
        
        if heading_match:
            # 先处理之前积累的段落文本
            if current_paragraph.strip():
                doc.add_paragraph(current_paragraph.strip())
                current_paragraph = ""
            
            # 处理标题
            heading_level = len(heading_match.group(1))  # #的数量
            heading_text = heading_match.group(2)  # 标题文本
            
            # 添加标题段落
            heading_para = doc.add_heading(heading_text, level=heading_level)
            
        elif line.strip() == "":
            # 空行：结束当前段落
            if current_paragraph.strip():
                doc.add_paragraph(current_paragraph.strip())
                current_paragraph = ""
            # 添加空段落保持间距
            doc.add_paragraph("")
            
        else:
            # 普通文本行：移除简单的markdown格式
            cleaned_line = clean_markdown_text(line)
            
            # 积累到当前段落
            if current_paragraph:
                current_paragraph += "\n" + cleaned_line
            else:
                current_paragraph = cleaned_line
    
    # 处理最后的段落
    if current_paragraph.strip():
        doc.add_paragraph(current_paragraph.strip())
    
    # 确定输出文件路径
    if word_file_path is None:
        md_path = Path(md_file_path)
        word_file_path = md_path.parent / f"{md_path.stem}.docx"
    
    # 保存Word文档
    try:
        doc.save(word_file_path)
        print(f"✅ 转换成功: {word_file_path}")
        return True
    except Exception as e:
        print(f"❌ 保存Word文件失败: {e}")
        return False

def clean_markdown_text(text):
    """
    清理简单的markdown格式，保留文本内容
    只处理基础格式，复杂格式直接忽略
    """
    # 移除加粗格式 **text** 或 __text__
    text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)
    text = re.sub(r'__(.*?)__', r'\1', text)
    
    # 移除斜体格式 *text* 或 _text_ (谨慎处理，避免误删)
    text = re.sub(r'(?<!\*)\*(?!\*)([^*]+)\*(?!\*)', r'\1', text)
    text = re.sub(r'(?<!_)_(?!_)([^_]+)_(?!_)', r'\1', text)
    
    # 移除行内代码格式 `text`
    text = re.sub(r'`([^`]+)`', r'\1', text)
    
    # 处理列表项：保留内容，移除列表标记
    text = re.sub(r'^(\s*)[-*+]\s+', r'\1• ', text)  # 无序列表转为项目符号
    text = re.sub(r'^(\s*)\d+\.\s+', r'\1', text)     # 有序列表移除编号
    
    # 移除链接格式 [text](url)，保留text
    text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)
    
    return text

def batch_convert(input_dir, output_dir=None):
    """
    批量转换目录中的所有Markdown文件
    
    Args:
        input_dir: 输入目录路径
        output_dir: 输出目录路径，如果为None则使用输入目录
    """
    input_path = Path(input_dir)
    
    if not input_path.exists():
        print(f"❌ 输入目录不存在: {input_dir}")
        return False
    
    if output_dir is None:
        output_path = input_path
    else:
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
    
    # 查找所有markdown文件
    md_files = list(input_path.glob("*.md"))
    
    if not md_files:
        print(f"❌ 在目录 {input_dir} 中未找到Markdown文件")
        return False
    
    print(f"📁 发现 {len(md_files)} 个Markdown文件")
    
    success_count = 0
    for md_file in md_files:
        print(f"\n🔄 转换: {md_file.name}")
        
        # 生成输出文件路径
        word_file = output_path / f"{md_file.stem}.docx"
        
        if convert_md_to_word(md_file, word_file):
            success_count += 1
        else:
            print(f"❌ 转换失败: {md_file.name}")
    
    print(f"\n📊 转换完成: {success_count}/{len(md_files)} 个文件成功")
    return success_count == len(md_files)

def main():
    parser = argparse.ArgumentParser(description="简单的Markdown转Word工具 - 重点处理标题结构")
    parser.add_argument("input", help="输入的Markdown文件或目录路径")
    parser.add_argument("-o", "--output", help="输出Word文件或目录路径（可选）")
    parser.add_argument("--batch", action="store_true", help="批量处理模式（当输入为目录时）")
    
    args = parser.parse_args()
    
    input_path = Path(args.input)
    
    if not input_path.exists():
        print(f"❌ 输入路径不存在: {args.input}")
        return 1
    
    print("📝 简单Markdown转Word工具")
    print("🎯 重点处理: 标题结构(#)")
    print("⚠️  忽略复杂格式: 表格、复杂列表等")
    print("=" * 50)
    
    if input_path.is_file():
        # 单文件转换
        if not input_path.suffix.lower() == '.md':
            print(f"❌ 输入文件不是Markdown文件: {args.input}")
            return 1
        
        success = convert_md_to_word(args.input, args.output)
        return 0 if success else 1
        
    elif input_path.is_dir():
        # 目录转换
        if not args.batch:
            print(f"❌ 输入是目录，请使用 --batch 参数进行批量转换")
            return 1
        
        success = batch_convert(args.input, args.output)
        return 0 if success else 1
    
    else:
        print(f"❌ 无效的输入路径: {args.input}")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main()) 